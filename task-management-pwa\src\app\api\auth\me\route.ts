import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 /api/auth/me called');

    const supabase = await createClient();

    // Use getUser() to validate the session (recommended by Supabase)
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      console.log('🔍 No valid session found');
      return NextResponse.json(
        { user: null, session: null },
        { status: 200 }
      );
    }

    console.log('✅ Valid session found for user:', user.email);
    console.log('🔍 User ID from auth:', user.id);

    // Use service role client to bypass RLS for user lookup
    const serviceSupabase = createServiceClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get user from our database using service role (bypasses RLS)
    const { data: dbUser, error: dbError } = await serviceSupabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();

    console.log('🔍 Database query result:', {
      dbUser: dbUser ? { id: dbUser.id, email: dbUser.email, approved: dbUser.approved } : null,
      dbError: dbError ? { code: dbError.code, message: dbError.message } : null
    });

    if (dbError || !dbUser) {
      // If user doesn't exist in our database, create them
      if (dbError?.code === 'PGRST116') {
        console.log('🔍 User not found in database, creating new user...');

        const newUser = {
          id: user.id,
          email: user.email!,
          name: user.user_metadata?.full_name || user.user_metadata?.name || user.email!,
          role: 'user' as const,
          approved: false,
        };

        const { data: createdUser, error: createError } = await supabase
          .from('users')
          .insert(newUser)
          .select()
          .single();

        if (createError) {
          console.error('❌ User creation error:', createError);
          return NextResponse.json(
            { user: null, session: null },
            { status: 200 }
          );
        }

        console.log('✅ New user created:', createdUser.email);
        return NextResponse.json({
          user: createdUser,
          session: { access_token: 'session_managed_by_cookies' }
        });
      }

      console.log('🔍 Database error:', dbError);
      return NextResponse.json(
        { user: null, session: null },
        { status: 200 }
      );
    }

    console.log('✅ User authenticated:', dbUser.email);
    return NextResponse.json({
      user: dbUser,
      session: { access_token: 'session_managed_by_cookies' }
    });

  } catch (error) {
    console.error('❌ Auth check error:', error);
    return NextResponse.json(
      { error: { code: 'INTERNAL_ERROR', message: 'Internal server error' } },
      { status: 500 }
    );
  }
}