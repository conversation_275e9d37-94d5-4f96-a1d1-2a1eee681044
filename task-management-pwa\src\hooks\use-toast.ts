import { toast as sonnerToast } from 'sonner';

export interface Toast {
  title: string;
  description?: string;
  variant?: 'default' | 'destructive';
  action?: React.ReactNode;
}

export function useToast() {
  const toast = ({ title, description, variant = 'default', action }: Toast) => {
    const message = description ? `${title}: ${description}` : title;

    if (variant === 'destructive') {
      sonnerToast.error(title, {
        description,
        action,
      });
    } else {
      sonnerToast.success(title, {
        description,
        action,
      });
    }
  };

  return { toast };
}