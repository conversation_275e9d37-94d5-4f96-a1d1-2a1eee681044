import { QueryClient, DefaultOptions, MutationCache, QueryCache } from '@tanstack/react-query';
import { toast } from 'sonner';

/**
 * Default query options for consistent behavior across the app
 */
const defaultQueryOptions: DefaultOptions = {
  queries: {
    // Cache data for 5 minutes by default
    staleTime: 5 * 60 * 1000,
    
    // Keep data in cache for 10 minutes after component unmount
    gcTime: 10 * 60 * 1000,
    
    // Retry failed requests 3 times with exponential backoff
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors (client errors)
      if (error?.status >= 400 && error?.status < 500) {
        return false;
      }
      
      // Retry up to 3 times for other errors
      return failureCount < 3;
    },
    
    // Retry delay with exponential backoff
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    
    // Refetch on window focus for critical data
    refetchOnWindowFocus: (query) => {
      // Only refetch critical queries on window focus
      const criticalQueries = ['auth', 'timer', 'notifications'];
      return criticalQueries.some(key => 
        query.queryKey.some(k => typeof k === 'string' && k.includes(key))
      );
    },
    
    // Refetch on reconnect
    refetchOnReconnect: true,
    
    // Don't refetch on mount if data is fresh
    refetchOnMount: (query) => {
      // Refetch if data is older than 1 minute
      return Date.now() - (query.state.dataUpdatedAt || 0) > 60 * 1000;
    },
  },
  
  mutations: {
    // Retry mutations once
    retry: 1,
    
    // Retry delay for mutations
    retryDelay: 1000,
  },
};

/**
 * Query cache configuration with global error handling
 */
const queryCache = new QueryCache({
  onError: (error: any, query) => {
    console.error('Query error:', error, 'Query:', query.queryKey);
    
    // Don't show toast for background refetches
    if (query.state.fetchStatus === 'fetching' && query.state.data !== undefined) {
      return;
    }
    
    // Show user-friendly error messages
    const errorMessage = getErrorMessage(error);

    toast.error('Error', {
      description: errorMessage,
    });
  },
  
  onSuccess: (data, query) => {
    // Log successful queries in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Query success:', query.queryKey, data);
    }
  },
});

/**
 * Mutation cache configuration with global error handling
 */
const mutationCache = new MutationCache({
  onError: (error: any, variables, context, mutation) => {
    console.error('Mutation error:', error, 'Variables:', variables);
    
    // Show user-friendly error messages for mutations
    const errorMessage = getErrorMessage(error);

    toast.error('Operation Failed', {
      description: errorMessage,
    });
  },
  
  onSuccess: (data, variables, context, mutation) => {
    // Log successful mutations in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Mutation success:', mutation.options.mutationKey, data);
    }
  },
});

/**
 * Extract user-friendly error messages from API responses
 */
function getErrorMessage(error: any): string {
  // Handle network errors
  if (!navigator.onLine) {
    return 'You appear to be offline. Please check your connection.';
  }
  
  // Handle API errors
  if (error?.response?.data?.error?.message) {
    return error.response.data.error.message;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  // Handle HTTP status codes
  if (error?.status) {
    switch (error.status) {
      case 401:
        return 'You are not authorized. Please log in again.';
      case 403:
        return 'You do not have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Server error. Please try again later.';
      case 503:
        return 'Service temporarily unavailable. Please try again later.';
      default:
        return `Request failed with status ${error.status}`;
    }
  }
  
  return 'An unexpected error occurred. Please try again.';
}

/**
 * Create and configure the query client
 */
export function createQueryClient(): QueryClient {
  return new QueryClient({
    defaultOptions: defaultQueryOptions,
    queryCache,
    mutationCache,
  });
}

/**
 * Global query client instance
 */
export const queryClient = createQueryClient();

/**
 * Query client utilities for common operations
 */
export const queryClientUtils = {
  /**
   * Invalidate queries by pattern
   */
  invalidateByPattern: async (pattern: string[]) => {
    await queryClient.invalidateQueries({
      queryKey: pattern,
    });
  },

  /**
   * Invalidate all queries for a specific entity
   */
  invalidateEntity: async (entity: string) => {
    await queryClient.invalidateQueries({
      queryKey: [entity],
    });
  },

  /**
   * Remove queries by pattern
   */
  removeByPattern: (pattern: string[]) => {
    queryClient.removeQueries({
      queryKey: pattern,
    });
  },

  /**
   * Set query data with optimistic updates
   */
  setOptimisticData: <T>(queryKey: string[], updater: (old: T | undefined) => T) => {
    queryClient.setQueryData(queryKey, updater);
  },

  /**
   * Get cached query data
   */
  getCachedData: <T>(queryKey: string[]): T | undefined => {
    return queryClient.getQueryData<T>(queryKey);
  },

  /**
   * Prefetch query data
   */
  prefetch: async (queryKey: string[], queryFn: () => Promise<any>) => {
    await queryClient.prefetchQuery({
      queryKey,
      queryFn,
    });
  },

  /**
   * Cancel outgoing queries
   */
  cancelQueries: async (queryKey?: string[]) => {
    await queryClient.cancelQueries({ queryKey });
  },

  /**
   * Reset all queries and cache
   */
  resetAll: async () => {
    await queryClient.resetQueries();
  },

  /**
   * Clear all cache
   */
  clearCache: () => {
    queryClient.clear();
  },

  /**
   * Get query cache stats
   */
  getCacheStats: () => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    return {
      totalQueries: queries.length,
      activeQueries: queries.filter(q => q.getObserversCount() > 0).length,
      staleQueries: queries.filter(q => q.isStale()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
      loadingQueries: queries.filter(q => q.state.fetchStatus === 'fetching').length,
    };
  },
};

/**
 * Development tools for debugging queries
 */
export const queryDevTools = {
  /**
   * Log all queries and their states
   */
  logAllQueries: () => {
    if (process.env.NODE_ENV !== 'development') return;
    
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    console.group('Query Cache State');
    queries.forEach(query => {
      console.log({
        queryKey: query.queryKey,
        status: query.state.status,
        fetchStatus: query.state.fetchStatus,
        dataUpdatedAt: new Date(query.state.dataUpdatedAt || 0),
        error: query.state.error,
        observers: query.getObserversCount(),
      });
    });
    console.groupEnd();
  },

  /**
   * Monitor query performance
   */
  monitorPerformance: () => {
    if (process.env.NODE_ENV !== 'development') return;
    
    const cache = queryClient.getQueryCache();
    
    cache.subscribe((event) => {
      if (event.type === 'queryUpdated') {
        const query = event.query;
        const duration = Date.now() - (query.state.fetchFailureReason as any)?.timestamp || 0;
        
        if (duration > 1000) {
          console.warn('Slow query detected:', {
            queryKey: query.queryKey,
            duration: `${duration}ms`,
          });
        }
      }
    });
  },

  /**
   * Track cache hit/miss ratio
   */
  trackCacheHitRatio: () => {
    if (process.env.NODE_ENV !== 'development') return;
    
    let hits = 0;
    let misses = 0;
    
    const cache = queryClient.getQueryCache();
    
    cache.subscribe((event) => {
      if (event.type === 'queryUpdated') {
        const query = event.query;
        
        if (query.state.fetchStatus === 'fetching' && query.state.data !== undefined) {
          hits++;
        } else if (query.state.fetchStatus === 'fetching') {
          misses++;
        }
        
        const total = hits + misses;
        if (total > 0 && total % 10 === 0) {
          console.log(`Cache hit ratio: ${((hits / total) * 100).toFixed(1)}% (${hits}/${total})`);
        }
      }
    });
  },
};
