"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\nfunction useAuth() {\n    var _authQuery_data, _authQuery_data1, _authQuery_data2, _authQuery_data_user, _authQuery_data3;\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const authQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: _lib_config__WEBPACK_IMPORTED_MODULE_1__.queryKeys.auth,\n        queryFn: {\n            \"useAuth.useQuery[authQuery]\": async ()=>{\n                try {\n                    const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get('/auth/me');\n                    if (!response.user || !response.session) {\n                        return null;\n                    }\n                    return {\n                        user: response.user,\n                        session: response.session\n                    };\n                } catch (error) {\n                    console.error('Auth check failed:', error);\n                    return null;\n                }\n            }\n        }[\"useAuth.useQuery[authQuery]\"],\n        staleTime: 5 * 60 * 1000,\n        retry: false\n    });\n    const loginMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: {\n            \"useAuth.useMutation[loginMutation]\": async (googleToken)=>{\n                return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/login', {\n                    googleToken\n                });\n            }\n        }[\"useAuth.useMutation[loginMutation]\"],\n        onSuccess: {\n            \"useAuth.useMutation[loginMutation]\": (data)=>{\n                // Update the auth query cache\n                queryClient.setQueryData(_lib_config__WEBPACK_IMPORTED_MODULE_1__.queryKeys.auth, {\n                    user: data.user,\n                    session: data.session\n                });\n                // Session is handled by the API, no need for client-side session management\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('Login successful', {\n                    description: \"Welcome back, \".concat(data.user.name, \"!\")\n                });\n            }\n        }[\"useAuth.useMutation[loginMutation]\"],\n        onError: {\n            \"useAuth.useMutation[loginMutation]\": (error)=>{\n                console.error('Login failed:', error);\n            }\n        }[\"useAuth.useMutation[loginMutation]\"]\n    });\n    const logoutMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: {\n            \"useAuth.useMutation[logoutMutation]\": async ()=>{\n                await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/logout');\n            }\n        }[\"useAuth.useMutation[logoutMutation]\"],\n        onSuccess: {\n            \"useAuth.useMutation[logoutMutation]\": ()=>{\n                // Clear all cached data\n                queryClient.clear();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('Logged out successfully');\n            }\n        }[\"useAuth.useMutation[logoutMutation]\"],\n        onError: {\n            \"useAuth.useMutation[logoutMutation]\": (error)=>{\n                console.error('Logout failed:', error);\n                // Still clear cache on error\n                queryClient.clear();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Logout failed', {\n                    description: 'Please try again'\n                });\n            }\n        }[\"useAuth.useMutation[logoutMutation]\"]\n    });\n    const signInWithGoogle = async ()=>{\n        try {\n            const { data, error } = await supabase.auth.signInWithOAuth({\n                provider: 'google',\n                options: {\n                    redirectTo: \"\".concat(window.location.origin, \"/auth/callback\")\n                }\n            });\n            if (error) {\n                throw error;\n            }\n            return data;\n        } catch (error) {\n            console.error('Google sign-in failed:', error);\n            throw error;\n        }\n    };\n    return {\n        user: ((_authQuery_data = authQuery.data) === null || _authQuery_data === void 0 ? void 0 : _authQuery_data.user) || null,\n        session: ((_authQuery_data1 = authQuery.data) === null || _authQuery_data1 === void 0 ? void 0 : _authQuery_data1.session) || null,\n        isLoading: authQuery.isLoading,\n        isAuthenticated: !!((_authQuery_data2 = authQuery.data) === null || _authQuery_data2 === void 0 ? void 0 : _authQuery_data2.user),\n        isApproved: ((_authQuery_data3 = authQuery.data) === null || _authQuery_data3 === void 0 ? void 0 : (_authQuery_data_user = _authQuery_data3.user) === null || _authQuery_data_user === void 0 ? void 0 : _authQuery_data_user.approved) || false,\n        login: loginMutation.mutate,\n        logout: logoutMutation.mutate,\n        signInWithGoogle,\n        isLoginLoading: loginMutation.isPending,\n        isLogoutLoading: logoutMutation.isPending,\n        refetch: authQuery.refetch\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuth.ts\n"));

/***/ })

});