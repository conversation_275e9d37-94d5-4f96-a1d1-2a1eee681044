"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\nfunction useAuth() {\n    var _authQuery_data, _authQuery_data1, _authQuery_data2, _authQuery_data_user, _authQuery_data3;\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const authQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: _lib_config__WEBPACK_IMPORTED_MODULE_1__.queryKeys.auth,\n        queryFn: {\n            \"useAuth.useQuery[authQuery]\": async ()=>{\n                try {\n                    const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get('/auth/me');\n                    if (!response.user || !response.session) {\n                        return null;\n                    }\n                    return {\n                        user: response.user,\n                        session: response.session\n                    };\n                } catch (error) {\n                    console.error('Auth check failed:', error);\n                    return null;\n                }\n            }\n        }[\"useAuth.useQuery[authQuery]\"],\n        staleTime: 5 * 60 * 1000,\n        retry: false\n    });\n    const loginMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: {\n            \"useAuth.useMutation[loginMutation]\": async (googleToken)=>{\n                return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/login', {\n                    googleToken\n                });\n            }\n        }[\"useAuth.useMutation[loginMutation]\"],\n        onSuccess: {\n            \"useAuth.useMutation[loginMutation]\": (data)=>{\n                // Update the auth query cache\n                queryClient.setQueryData(_lib_config__WEBPACK_IMPORTED_MODULE_1__.queryKeys.auth, {\n                    user: data.user,\n                    session: data.session\n                });\n                // Session is handled by the API, no need for client-side session management\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('Login successful', {\n                    description: \"Welcome back, \".concat(data.user.name, \"!\")\n                });\n            }\n        }[\"useAuth.useMutation[loginMutation]\"],\n        onError: {\n            \"useAuth.useMutation[loginMutation]\": (error)=>{\n                console.error('Login failed:', error);\n            }\n        }[\"useAuth.useMutation[loginMutation]\"]\n    });\n    const logoutMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: {\n            \"useAuth.useMutation[logoutMutation]\": async ()=>{\n                await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/logout');\n            }\n        }[\"useAuth.useMutation[logoutMutation]\"],\n        onSuccess: {\n            \"useAuth.useMutation[logoutMutation]\": ()=>{\n                // Clear all cached data\n                queryClient.clear();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('Logged out successfully');\n            }\n        }[\"useAuth.useMutation[logoutMutation]\"],\n        onError: {\n            \"useAuth.useMutation[logoutMutation]\": (error)=>{\n                console.error('Logout failed:', error);\n                // Still clear cache on error\n                queryClient.clear();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Logout failed', {\n                    description: 'Please try again'\n                });\n            }\n        }[\"useAuth.useMutation[logoutMutation]\"]\n    });\n    const signInWithGoogle = async ()=>{\n        try {\n            // Redirect to API endpoint for Google OAuth\n            window.location.href = '/api/auth/google';\n        } catch (error) {\n            console.error('Google sign-in failed:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Sign-in failed', {\n                description: 'Please try again'\n            });\n            throw error;\n        }\n    };\n    return {\n        user: ((_authQuery_data = authQuery.data) === null || _authQuery_data === void 0 ? void 0 : _authQuery_data.user) || null,\n        session: ((_authQuery_data1 = authQuery.data) === null || _authQuery_data1 === void 0 ? void 0 : _authQuery_data1.session) || null,\n        isLoading: authQuery.isLoading,\n        isAuthenticated: !!((_authQuery_data2 = authQuery.data) === null || _authQuery_data2 === void 0 ? void 0 : _authQuery_data2.user),\n        isApproved: ((_authQuery_data3 = authQuery.data) === null || _authQuery_data3 === void 0 ? void 0 : (_authQuery_data_user = _authQuery_data3.user) === null || _authQuery_data_user === void 0 ? void 0 : _authQuery_data_user.approved) || false,\n        login: loginMutation.mutate,\n        logout: logoutMutation.mutate,\n        signInWithGoogle,\n        isLoginLoading: loginMutation.isPending,\n        isLogoutLoading: logoutMutation.isPending,\n        refetch: authQuery.refetch\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuth.ts\n"));

/***/ })

});