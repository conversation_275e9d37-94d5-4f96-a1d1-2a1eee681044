"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_TIMEZONE: () => (/* binding */ DEFAULT_TIMEZONE),\n/* harmony export */   TIMER_CONFIG: () => (/* binding */ TIMER_CONFIG),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   getConfig: () => (/* binding */ getConfig),\n/* harmony export */   queryKeys: () => (/* binding */ queryKeys)\n/* harmony export */ });\n// Application configuration\nconst config = {\n    development: {\n        apiUrl: 'http://localhost:3000/api',\n        supabaseUrl: \"https://japstojgshtcihggoxue.supabase.co\",\n        domain: 'localhost:3000'\n    },\n    production: {\n        apiUrl: 'https://your-domain.com/api',\n        supabaseUrl: \"https://japstojgshtcihggoxue.supabase.co\",\n        domain: 'your-domain.com'\n    }\n};\nconst getConfig = ()=>{\n    return config[\"development\"] || config.development;\n};\n// Default timezone configuration\nconst DEFAULT_TIMEZONE = 'America/Chicago';\n// Timer configuration\nconst TIMER_CONFIG = {\n    maxDurationHours: 6,\n    updateIntervalMs: 1000\n};\n// Query keys for TanStack Query\nconst queryKeys = {\n    auth: [\n        'auth'\n    ],\n    user: (id)=>[\n            'user',\n            id\n        ],\n    users: (filters)=>[\n            'users',\n            filters\n        ],\n    tasks: (filters)=>[\n            'tasks',\n            filters\n        ],\n    task: (id)=>[\n            'task',\n            id\n        ],\n    timer: [\n        'timer'\n    ],\n    reports: (params)=>[\n            'reports',\n            params\n        ],\n    notifications: [\n        'notifications'\n    ],\n    notificationStats: [\n        'notifications',\n        'stats'\n    ]\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvY29uZmlnLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUEsNEJBQTRCO0FBQ3JCLE1BQU1BLFNBQVM7SUFDcEJDLGFBQWE7UUFDWEMsUUFBUTtRQUNSQyxhQUFhQywwQ0FBb0M7UUFDakRHLFFBQVE7SUFDVjtJQUNBQyxZQUFZO1FBQ1ZOLFFBQVE7UUFDUkMsYUFBYUMsMENBQW9DO1FBQ2pERyxRQUFRO0lBQ1Y7QUFDRixFQUFFO0FBRUssTUFBTUUsWUFBWTtJQUN2QixPQUFPVCxNQUFNLENBWEcsY0FXMEMsSUFBSUEsT0FBT0MsV0FBVztBQUNsRixFQUFFO0FBRUYsaUNBQWlDO0FBQzFCLE1BQU1TLG1CQUFtQixrQkFBa0I7QUFFbEQsc0JBQXNCO0FBQ2YsTUFBTUMsZUFBZTtJQUMxQkMsa0JBQWtCO0lBQ2xCQyxrQkFBa0I7QUFDcEIsRUFBRTtBQUVGLGdDQUFnQztBQUN6QixNQUFNQyxZQUFZO0lBQ3ZCQyxNQUFNO1FBQUM7S0FBTztJQUNkQyxNQUFNLENBQUNDLEtBQWU7WUFBQztZQUFRQTtTQUFHO0lBQ2xDQyxPQUFPLENBQUNDLFVBQXNDO1lBQUM7WUFBU0E7U0FBUTtJQUNoRUMsT0FBTyxDQUFDRCxVQUFzQztZQUFDO1lBQVNBO1NBQVE7SUFDaEVFLE1BQU0sQ0FBQ0osS0FBZTtZQUFDO1lBQVFBO1NBQUc7SUFDbENLLE9BQU87UUFBQztLQUFRO0lBQ2hCQyxTQUFTLENBQUNDLFNBQW9DO1lBQUM7WUFBV0E7U0FBTztJQUNqRUMsZUFBZTtRQUFDO0tBQWdCO0lBQ2hDQyxtQkFBbUI7UUFBQztRQUFpQjtLQUFRO0FBQy9DLEVBQVciLCJzb3VyY2VzIjpbIkQ6XFxBSVxcY2FuZGlkLXRhc2stbWdtdFxcdGFzay1tYW5hZ2VtZW50LXB3YVxcc3JjXFxsaWJcXGNvbmZpZy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBBcHBsaWNhdGlvbiBjb25maWd1cmF0aW9uXHJcbmV4cG9ydCBjb25zdCBjb25maWcgPSB7XHJcbiAgZGV2ZWxvcG1lbnQ6IHtcclxuICAgIGFwaVVybDogJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9hcGknLFxyXG4gICAgc3VwYWJhc2VVcmw6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXHJcbiAgICBkb21haW46ICdsb2NhbGhvc3Q6MzAwMCcsXHJcbiAgfSxcclxuICBwcm9kdWN0aW9uOiB7XHJcbiAgICBhcGlVcmw6ICdodHRwczovL3lvdXItZG9tYWluLmNvbS9hcGknLFxyXG4gICAgc3VwYWJhc2VVcmw6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXHJcbiAgICBkb21haW46ICd5b3VyLWRvbWFpbi5jb20nLFxyXG4gIH0sXHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgZ2V0Q29uZmlnID0gKCkgPT4ge1xyXG4gIHJldHVybiBjb25maWdbcHJvY2Vzcy5lbnYuTk9ERV9FTlYgYXMga2V5b2YgdHlwZW9mIGNvbmZpZ10gfHwgY29uZmlnLmRldmVsb3BtZW50O1xyXG59O1xyXG5cclxuLy8gRGVmYXVsdCB0aW1lem9uZSBjb25maWd1cmF0aW9uXHJcbmV4cG9ydCBjb25zdCBERUZBVUxUX1RJTUVaT05FID0gJ0FtZXJpY2EvQ2hpY2Fnbyc7XHJcblxyXG4vLyBUaW1lciBjb25maWd1cmF0aW9uXHJcbmV4cG9ydCBjb25zdCBUSU1FUl9DT05GSUcgPSB7XHJcbiAgbWF4RHVyYXRpb25Ib3VyczogNixcclxuICB1cGRhdGVJbnRlcnZhbE1zOiAxMDAwLFxyXG59O1xyXG5cclxuLy8gUXVlcnkga2V5cyBmb3IgVGFuU3RhY2sgUXVlcnlcclxuZXhwb3J0IGNvbnN0IHF1ZXJ5S2V5cyA9IHtcclxuICBhdXRoOiBbJ2F1dGgnXSBhcyBjb25zdCxcclxuICB1c2VyOiAoaWQ6IHN0cmluZykgPT4gWyd1c2VyJywgaWRdIGFzIGNvbnN0LFxyXG4gIHVzZXJzOiAoZmlsdGVycz86IFJlY29yZDxzdHJpbmcsIHVua25vd24+KSA9PiBbJ3VzZXJzJywgZmlsdGVyc10gYXMgY29uc3QsXHJcbiAgdGFza3M6IChmaWx0ZXJzPzogUmVjb3JkPHN0cmluZywgdW5rbm93bj4pID0+IFsndGFza3MnLCBmaWx0ZXJzXSBhcyBjb25zdCxcclxuICB0YXNrOiAoaWQ6IHN0cmluZykgPT4gWyd0YXNrJywgaWRdIGFzIGNvbnN0LFxyXG4gIHRpbWVyOiBbJ3RpbWVyJ10gYXMgY29uc3QsXHJcbiAgcmVwb3J0czogKHBhcmFtczogUmVjb3JkPHN0cmluZywgdW5rbm93bj4pID0+IFsncmVwb3J0cycsIHBhcmFtc10gYXMgY29uc3QsXHJcbiAgbm90aWZpY2F0aW9uczogWydub3RpZmljYXRpb25zJ10gYXMgY29uc3QsXHJcbiAgbm90aWZpY2F0aW9uU3RhdHM6IFsnbm90aWZpY2F0aW9ucycsICdzdGF0cyddIGFzIGNvbnN0LFxyXG59IGFzIGNvbnN0OyJdLCJuYW1lcyI6WyJjb25maWciLCJkZXZlbG9wbWVudCIsImFwaVVybCIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsImRvbWFpbiIsInByb2R1Y3Rpb24iLCJnZXRDb25maWciLCJERUZBVUxUX1RJTUVaT05FIiwiVElNRVJfQ09ORklHIiwibWF4RHVyYXRpb25Ib3VycyIsInVwZGF0ZUludGVydmFsTXMiLCJxdWVyeUtleXMiLCJhdXRoIiwidXNlciIsImlkIiwidXNlcnMiLCJmaWx0ZXJzIiwidGFza3MiLCJ0YXNrIiwidGltZXIiLCJyZXBvcnRzIiwicGFyYW1zIiwibm90aWZpY2F0aW9ucyIsIm5vdGlmaWNhdGlvblN0YXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerClient: () => (/* binding */ createServerClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\nconst supabaseUrl = \"https://japstojgshtcihggoxue.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImphcHN0b2pnc2h0Y2loZ2dveHVlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM4OTAxMTksImV4cCI6MjA2OTQ2NjExOX0.JbGMI45J-0Y0BxB3jDIYmU2rU5d9hhGZxj7Z0Jf7Vcs\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n// Server-side client for API routes\nconst createServerClient = ()=>{\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://japstojgshtcihggoxue.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ })

});