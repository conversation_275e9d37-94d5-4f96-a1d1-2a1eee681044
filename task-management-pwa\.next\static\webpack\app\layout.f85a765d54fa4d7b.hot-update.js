"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"829ad635f37c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcQUlcXGNhbmRpZC10YXNrLW1nbXRcXHRhc2stbWFuYWdlbWVudC1wd2FcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjgyOWFkNjM1ZjM3Y1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n\n\n\nfunction useAuth() {\n    var _authQuery_data, _authQuery_data1, _authQuery_data2, _authQuery_data_user, _authQuery_data3;\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const authQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        queryKey: _lib_config__WEBPACK_IMPORTED_MODULE_1__.queryKeys.auth,\n        queryFn: {\n            \"useAuth.useQuery[authQuery]\": async ()=>{\n                try {\n                    const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get('/auth/me');\n                    if (!response.user || !response.session) {\n                        return null;\n                    }\n                    return {\n                        user: response.user,\n                        session: response.session\n                    };\n                } catch (error) {\n                    console.error('Auth check failed:', error);\n                    return null;\n                }\n            }\n        }[\"useAuth.useQuery[authQuery]\"],\n        staleTime: 5 * 60 * 1000,\n        retry: false\n    });\n    const loginMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: {\n            \"useAuth.useMutation[loginMutation]\": async (googleToken)=>{\n                return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/login', {\n                    googleToken\n                });\n            }\n        }[\"useAuth.useMutation[loginMutation]\"],\n        onSuccess: {\n            \"useAuth.useMutation[loginMutation]\": (data)=>{\n                // Update the auth query cache\n                queryClient.setQueryData(_lib_config__WEBPACK_IMPORTED_MODULE_1__.queryKeys.auth, {\n                    user: data.user,\n                    session: data.session\n                });\n                // Set the session in Supabase client\n                if (data.session.token) {\n                    const expiresAt = data.session.expiresAt;\n                    const expiresIn = expiresAt - Math.floor(Date.now() / 1000);\n                    supabase.auth.setSession({\n                        access_token: data.session.token,\n                        refresh_token: '',\n                        expires_in: expiresIn,\n                        token_type: 'bearer',\n                        user: data.user\n                    });\n                }\n            }\n        }[\"useAuth.useMutation[loginMutation]\"],\n        onError: {\n            \"useAuth.useMutation[loginMutation]\": (error)=>{\n                console.error('Login failed:', error);\n            }\n        }[\"useAuth.useMutation[loginMutation]\"]\n    });\n    const logoutMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: {\n            \"useAuth.useMutation[logoutMutation]\": async ()=>{\n                await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/logout');\n            }\n        }[\"useAuth.useMutation[logoutMutation]\"],\n        onSuccess: {\n            \"useAuth.useMutation[logoutMutation]\": ()=>{\n                // Clear all cached data\n                queryClient.clear();\n                // Sign out from Supabase client\n                supabase.auth.signOut();\n            }\n        }[\"useAuth.useMutation[logoutMutation]\"],\n        onError: {\n            \"useAuth.useMutation[logoutMutation]\": (error)=>{\n                console.error('Logout failed:', error);\n                // Still clear cache and sign out on error\n                queryClient.clear();\n                supabase.auth.signOut();\n            }\n        }[\"useAuth.useMutation[logoutMutation]\"]\n    });\n    const signInWithGoogle = async ()=>{\n        try {\n            const { data, error } = await supabase.auth.signInWithOAuth({\n                provider: 'google',\n                options: {\n                    redirectTo: \"\".concat(window.location.origin, \"/auth/callback\")\n                }\n            });\n            if (error) {\n                throw error;\n            }\n            return data;\n        } catch (error) {\n            console.error('Google sign-in failed:', error);\n            throw error;\n        }\n    };\n    return {\n        user: ((_authQuery_data = authQuery.data) === null || _authQuery_data === void 0 ? void 0 : _authQuery_data.user) || null,\n        session: ((_authQuery_data1 = authQuery.data) === null || _authQuery_data1 === void 0 ? void 0 : _authQuery_data1.session) || null,\n        isLoading: authQuery.isLoading,\n        isAuthenticated: !!((_authQuery_data2 = authQuery.data) === null || _authQuery_data2 === void 0 ? void 0 : _authQuery_data2.user),\n        isApproved: ((_authQuery_data3 = authQuery.data) === null || _authQuery_data3 === void 0 ? void 0 : (_authQuery_data_user = _authQuery_data3.user) === null || _authQuery_data_user === void 0 ? void 0 : _authQuery_data_user.approved) || false,\n        login: loginMutation.mutate,\n        logout: logoutMutation.mutate,\n        signInWithGoogle,\n        isLoginLoading: loginMutation.isPending,\n        isLogoutLoading: logoutMutation.isPending,\n        refetch: authQuery.refetch\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VBdXRoLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE4RTtBQUNqQztBQUNKO0FBYWxDLFNBQVNLO1FBZ0dOQyxpQkFDR0Esa0JBRVVBLGtCQUNQQSxzQkFBQUE7SUFuR2QsTUFBTUMsY0FBY0wscUVBQWNBO0lBRWxDLE1BQU1JLFlBQVlOLCtEQUFRQSxDQUFDO1FBQ3pCUSxVQUFVSixrREFBU0EsQ0FBQ0ssSUFBSTtRQUN4QkMsT0FBTzsyQ0FBRTtnQkFDUCxJQUFJO29CQUNGLE1BQU1DLFdBQVcsTUFBTVIsc0RBQVNBLENBQUNTLEdBQUcsQ0FBc0M7b0JBRTFFLElBQUksQ0FBQ0QsU0FBU0UsSUFBSSxJQUFJLENBQUNGLFNBQVNHLE9BQU8sRUFBRTt3QkFDdkMsT0FBTztvQkFDVDtvQkFFQSxPQUFPO3dCQUNMRCxNQUFNRixTQUFTRSxJQUFJO3dCQUNuQkMsU0FBU0gsU0FBU0csT0FBTztvQkFDM0I7Z0JBQ0YsRUFBRSxPQUFPQyxPQUFPO29CQUNkQyxRQUFRRCxLQUFLLENBQUMsc0JBQXNCQTtvQkFDcEMsT0FBTztnQkFDVDtZQUNGOztRQUNBRSxXQUFXLElBQUksS0FBSztRQUNwQkMsT0FBTztJQUNUO0lBRUEsTUFBTUMsZ0JBQWdCbEIsa0VBQVdBLENBQUM7UUFDaENtQixVQUFVO2tEQUFFLE9BQU9DO2dCQUNqQixPQUFPbEIsc0RBQVNBLENBQUNtQixJQUFJLENBQWdCLGVBQWU7b0JBQUVEO2dCQUFZO1lBQ3BFOztRQUNBRSxTQUFTO2tEQUFFLENBQUNDO2dCQUNWLDhCQUE4QjtnQkFDOUJqQixZQUFZa0IsWUFBWSxDQUFDckIsa0RBQVNBLENBQUNLLElBQUksRUFBRTtvQkFDdkNJLE1BQU1XLEtBQUtYLElBQUk7b0JBQ2ZDLFNBQVNVLEtBQUtWLE9BQU87Z0JBQ3ZCO2dCQUVBLHFDQUFxQztnQkFDckMsSUFBSVUsS0FBS1YsT0FBTyxDQUFDWSxLQUFLLEVBQUU7b0JBQ3RCLE1BQU1DLFlBQVlILEtBQUtWLE9BQU8sQ0FBQ2EsU0FBUztvQkFDeEMsTUFBTUMsWUFBWUQsWUFBWUUsS0FBS0MsS0FBSyxDQUFDQyxLQUFLQyxHQUFHLEtBQUs7b0JBRXREQyxTQUFTeEIsSUFBSSxDQUFDeUIsVUFBVSxDQUFDO3dCQUN2QkMsY0FBY1gsS0FBS1YsT0FBTyxDQUFDWSxLQUFLO3dCQUNoQ1UsZUFBZTt3QkFDZkMsWUFBWVQ7d0JBQ1pVLFlBQVk7d0JBQ1p6QixNQUFNVyxLQUFLWCxJQUFJO29CQUNqQjtnQkFDRjtZQUNGOztRQUNBMEIsT0FBTztrREFBRSxDQUFDeEI7Z0JBQ1JDLFFBQVFELEtBQUssQ0FBQyxpQkFBaUJBO1lBQ2pDOztJQUNGO0lBRUEsTUFBTXlCLGlCQUFpQnZDLGtFQUFXQSxDQUFDO1FBQ2pDbUIsVUFBVTttREFBRTtnQkFDVixNQUFNakIsc0RBQVNBLENBQUNtQixJQUFJLENBQUM7WUFDdkI7O1FBQ0FDLFNBQVM7bURBQUU7Z0JBQ1Qsd0JBQXdCO2dCQUN4QmhCLFlBQVlrQyxLQUFLO2dCQUVqQixnQ0FBZ0M7Z0JBQ2hDUixTQUFTeEIsSUFBSSxDQUFDaUMsT0FBTztZQUN2Qjs7UUFDQUgsT0FBTzttREFBRSxDQUFDeEI7Z0JBQ1JDLFFBQVFELEtBQUssQ0FBQyxrQkFBa0JBO2dCQUNoQywwQ0FBMEM7Z0JBQzFDUixZQUFZa0MsS0FBSztnQkFDakJSLFNBQVN4QixJQUFJLENBQUNpQyxPQUFPO1lBQ3ZCOztJQUNGO0lBRUEsTUFBTUMsbUJBQW1CO1FBQ3ZCLElBQUk7WUFDRixNQUFNLEVBQUVuQixJQUFJLEVBQUVULEtBQUssRUFBRSxHQUFHLE1BQU1rQixTQUFTeEIsSUFBSSxDQUFDbUMsZUFBZSxDQUFDO2dCQUMxREMsVUFBVTtnQkFDVkMsU0FBUztvQkFDUEMsWUFBWSxHQUEwQixPQUF2QkMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNLEVBQUM7Z0JBQ3hDO1lBQ0Y7WUFFQSxJQUFJbkMsT0FBTztnQkFDVCxNQUFNQTtZQUNSO1lBRUEsT0FBT1M7UUFDVCxFQUFFLE9BQU9ULE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7WUFDeEMsTUFBTUE7UUFDUjtJQUNGO0lBRUEsT0FBTztRQUNMRixNQUFNUCxFQUFBQSxrQkFBQUEsVUFBVWtCLElBQUksY0FBZGxCLHNDQUFBQSxnQkFBZ0JPLElBQUksS0FBSTtRQUM5QkMsU0FBU1IsRUFBQUEsbUJBQUFBLFVBQVVrQixJQUFJLGNBQWRsQix1Q0FBQUEsaUJBQWdCUSxPQUFPLEtBQUk7UUFDcENxQyxXQUFXN0MsVUFBVTZDLFNBQVM7UUFDOUJDLGlCQUFpQixDQUFDLEdBQUM5QyxtQkFBQUEsVUFBVWtCLElBQUksY0FBZGxCLHVDQUFBQSxpQkFBZ0JPLElBQUk7UUFDdkN3QyxZQUFZL0MsRUFBQUEsbUJBQUFBLFVBQVVrQixJQUFJLGNBQWRsQix3Q0FBQUEsdUJBQUFBLGlCQUFnQk8sSUFBSSxjQUFwQlAsMkNBQUFBLHFCQUFzQmdELFFBQVEsS0FBSTtRQUM5Q0MsT0FBT3BDLGNBQWNxQyxNQUFNO1FBQzNCQyxRQUFRakIsZUFBZWdCLE1BQU07UUFDN0JiO1FBQ0FlLGdCQUFnQnZDLGNBQWN3QyxTQUFTO1FBQ3ZDQyxpQkFBaUJwQixlQUFlbUIsU0FBUztRQUN6Q0UsU0FBU3ZELFVBQVV1RCxPQUFPO0lBQzVCO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxBSVxcY2FuZGlkLXRhc2stbWdtdFxcdGFzay1tYW5hZ2VtZW50LXB3YVxcc3JjXFxob29rc1xcdXNlQXV0aC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VRdWVyeSwgdXNlTXV0YXRpb24sIHVzZVF1ZXJ5Q2xpZW50IH0gZnJvbSAnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5JztcclxuaW1wb3J0IHsgYXBpQ2xpZW50IH0gZnJvbSAnQC9saWIvYXBpLWNsaWVudCc7XHJcbmltcG9ydCB7IHF1ZXJ5S2V5cyB9IGZyb20gJ0AvbGliL2NvbmZpZyc7XHJcbmltcG9ydCB7IFVzZXIsIEF1dGhSZXN1bHQgfSBmcm9tICdAL3R5cGVzJztcclxuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdzb25uZXInO1xyXG5cclxuaW50ZXJmYWNlIExvZ2luUmVzcG9uc2Uge1xyXG4gIHVzZXI6IFVzZXI7XHJcbiAgc2Vzc2lvbjoge1xyXG4gICAgdG9rZW46IHN0cmluZztcclxuICAgIGV4cGlyZXNBdDogbnVtYmVyO1xyXG4gIH07XHJcbiAgYXBwcm92ZWQ6IGJvb2xlYW47XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiB1c2VBdXRoKCkge1xyXG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKTtcclxuXHJcbiAgY29uc3QgYXV0aFF1ZXJ5ID0gdXNlUXVlcnkoe1xyXG4gICAgcXVlcnlLZXk6IHF1ZXJ5S2V5cy5hdXRoLFxyXG4gICAgcXVlcnlGbjogYXN5bmMgKCk6IFByb21pc2U8QXV0aFJlc3VsdCB8IG51bGw+ID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQ8eyB1c2VyOiBVc2VyIHwgbnVsbDsgc2Vzc2lvbjogYW55IH0+KCcvYXV0aC9tZScpO1xyXG4gICAgICAgIFxyXG4gICAgICAgIGlmICghcmVzcG9uc2UudXNlciB8fCAhcmVzcG9uc2Uuc2Vzc2lvbikge1xyXG4gICAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgdXNlcjogcmVzcG9uc2UudXNlcixcclxuICAgICAgICAgIHNlc3Npb246IHJlc3BvbnNlLnNlc3Npb24sXHJcbiAgICAgICAgfTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdBdXRoIGNoZWNrIGZhaWxlZDonLCBlcnJvcik7XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgIH1cclxuICAgIH0sXHJcbiAgICBzdGFsZVRpbWU6IDUgKiA2MCAqIDEwMDAsIC8vIDUgbWludXRlc1xyXG4gICAgcmV0cnk6IGZhbHNlLFxyXG4gIH0pO1xyXG5cclxuICBjb25zdCBsb2dpbk11dGF0aW9uID0gdXNlTXV0YXRpb24oe1xyXG4gICAgbXV0YXRpb25GbjogYXN5bmMgKGdvb2dsZVRva2VuOiBzdHJpbmcpOiBQcm9taXNlPExvZ2luUmVzcG9uc2U+ID0+IHtcclxuICAgICAgcmV0dXJuIGFwaUNsaWVudC5wb3N0PExvZ2luUmVzcG9uc2U+KCcvYXV0aC9sb2dpbicsIHsgZ29vZ2xlVG9rZW4gfSk7XHJcbiAgICB9LFxyXG4gICAgb25TdWNjZXNzOiAoZGF0YSkgPT4ge1xyXG4gICAgICAvLyBVcGRhdGUgdGhlIGF1dGggcXVlcnkgY2FjaGVcclxuICAgICAgcXVlcnlDbGllbnQuc2V0UXVlcnlEYXRhKHF1ZXJ5S2V5cy5hdXRoLCB7XHJcbiAgICAgICAgdXNlcjogZGF0YS51c2VyLFxyXG4gICAgICAgIHNlc3Npb246IGRhdGEuc2Vzc2lvbixcclxuICAgICAgfSk7XHJcbiAgICAgIFxyXG4gICAgICAvLyBTZXQgdGhlIHNlc3Npb24gaW4gU3VwYWJhc2UgY2xpZW50XHJcbiAgICAgIGlmIChkYXRhLnNlc3Npb24udG9rZW4pIHtcclxuICAgICAgICBjb25zdCBleHBpcmVzQXQgPSBkYXRhLnNlc3Npb24uZXhwaXJlc0F0O1xyXG4gICAgICAgIGNvbnN0IGV4cGlyZXNJbiA9IGV4cGlyZXNBdCAtIE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApO1xyXG4gICAgICAgIFxyXG4gICAgICAgIHN1cGFiYXNlLmF1dGguc2V0U2Vzc2lvbih7XHJcbiAgICAgICAgICBhY2Nlc3NfdG9rZW46IGRhdGEuc2Vzc2lvbi50b2tlbixcclxuICAgICAgICAgIHJlZnJlc2hfdG9rZW46ICcnLCAvLyBXZSdsbCBoYW5kbGUgcmVmcmVzaCB0aHJvdWdoIEdvb2dsZVxyXG4gICAgICAgICAgZXhwaXJlc19pbjogZXhwaXJlc0luLFxyXG4gICAgICAgICAgdG9rZW5fdHlwZTogJ2JlYXJlcicsXHJcbiAgICAgICAgICB1c2VyOiBkYXRhLnVzZXIgYXMgYW55LFxyXG4gICAgICAgIH0gYXMgYW55KTtcclxuICAgICAgfVxyXG4gICAgfSxcclxuICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdMb2dpbiBmYWlsZWQ6JywgZXJyb3IpO1xyXG4gICAgfSxcclxuICB9KTtcclxuXHJcbiAgY29uc3QgbG9nb3V0TXV0YXRpb24gPSB1c2VNdXRhdGlvbih7XHJcbiAgICBtdXRhdGlvbkZuOiBhc3luYyAoKTogUHJvbWlzZTx2b2lkPiA9PiB7XHJcbiAgICAgIGF3YWl0IGFwaUNsaWVudC5wb3N0KCcvYXV0aC9sb2dvdXQnKTtcclxuICAgIH0sXHJcbiAgICBvblN1Y2Nlc3M6ICgpID0+IHtcclxuICAgICAgLy8gQ2xlYXIgYWxsIGNhY2hlZCBkYXRhXHJcbiAgICAgIHF1ZXJ5Q2xpZW50LmNsZWFyKCk7XHJcbiAgICAgIFxyXG4gICAgICAvLyBTaWduIG91dCBmcm9tIFN1cGFiYXNlIGNsaWVudFxyXG4gICAgICBzdXBhYmFzZS5hdXRoLnNpZ25PdXQoKTtcclxuICAgIH0sXHJcbiAgICBvbkVycm9yOiAoZXJyb3IpID0+IHtcclxuICAgICAgY29uc29sZS5lcnJvcignTG9nb3V0IGZhaWxlZDonLCBlcnJvcik7XHJcbiAgICAgIC8vIFN0aWxsIGNsZWFyIGNhY2hlIGFuZCBzaWduIG91dCBvbiBlcnJvclxyXG4gICAgICBxdWVyeUNsaWVudC5jbGVhcigpO1xyXG4gICAgICBzdXBhYmFzZS5hdXRoLnNpZ25PdXQoKTtcclxuICAgIH0sXHJcbiAgfSk7XHJcblxyXG4gIGNvbnN0IHNpZ25JbldpdGhHb29nbGUgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25JbldpdGhPQXV0aCh7XHJcbiAgICAgICAgcHJvdmlkZXI6ICdnb29nbGUnLFxyXG4gICAgICAgIG9wdGlvbnM6IHtcclxuICAgICAgICAgIHJlZGlyZWN0VG86IGAke3dpbmRvdy5sb2NhdGlvbi5vcmlnaW59L2F1dGgvY2FsbGJhY2tgLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKGVycm9yKSB7XHJcbiAgICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBkYXRhO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignR29vZ2xlIHNpZ24taW4gZmFpbGVkOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIHtcclxuICAgIHVzZXI6IGF1dGhRdWVyeS5kYXRhPy51c2VyIHx8IG51bGwsXHJcbiAgICBzZXNzaW9uOiBhdXRoUXVlcnkuZGF0YT8uc2Vzc2lvbiB8fCBudWxsLFxyXG4gICAgaXNMb2FkaW5nOiBhdXRoUXVlcnkuaXNMb2FkaW5nLFxyXG4gICAgaXNBdXRoZW50aWNhdGVkOiAhIWF1dGhRdWVyeS5kYXRhPy51c2VyLFxyXG4gICAgaXNBcHByb3ZlZDogYXV0aFF1ZXJ5LmRhdGE/LnVzZXI/LmFwcHJvdmVkIHx8IGZhbHNlLFxyXG4gICAgbG9naW46IGxvZ2luTXV0YXRpb24ubXV0YXRlLFxyXG4gICAgbG9nb3V0OiBsb2dvdXRNdXRhdGlvbi5tdXRhdGUsXHJcbiAgICBzaWduSW5XaXRoR29vZ2xlLFxyXG4gICAgaXNMb2dpbkxvYWRpbmc6IGxvZ2luTXV0YXRpb24uaXNQZW5kaW5nLFxyXG4gICAgaXNMb2dvdXRMb2FkaW5nOiBsb2dvdXRNdXRhdGlvbi5pc1BlbmRpbmcsXHJcbiAgICByZWZldGNoOiBhdXRoUXVlcnkucmVmZXRjaCxcclxuICB9O1xyXG59Il0sIm5hbWVzIjpbInVzZVF1ZXJ5IiwidXNlTXV0YXRpb24iLCJ1c2VRdWVyeUNsaWVudCIsImFwaUNsaWVudCIsInF1ZXJ5S2V5cyIsInVzZUF1dGgiLCJhdXRoUXVlcnkiLCJxdWVyeUNsaWVudCIsInF1ZXJ5S2V5IiwiYXV0aCIsInF1ZXJ5Rm4iLCJyZXNwb25zZSIsImdldCIsInVzZXIiLCJzZXNzaW9uIiwiZXJyb3IiLCJjb25zb2xlIiwic3RhbGVUaW1lIiwicmV0cnkiLCJsb2dpbk11dGF0aW9uIiwibXV0YXRpb25GbiIsImdvb2dsZVRva2VuIiwicG9zdCIsIm9uU3VjY2VzcyIsImRhdGEiLCJzZXRRdWVyeURhdGEiLCJ0b2tlbiIsImV4cGlyZXNBdCIsImV4cGlyZXNJbiIsIk1hdGgiLCJmbG9vciIsIkRhdGUiLCJub3ciLCJzdXBhYmFzZSIsInNldFNlc3Npb24iLCJhY2Nlc3NfdG9rZW4iLCJyZWZyZXNoX3Rva2VuIiwiZXhwaXJlc19pbiIsInRva2VuX3R5cGUiLCJvbkVycm9yIiwibG9nb3V0TXV0YXRpb24iLCJjbGVhciIsInNpZ25PdXQiLCJzaWduSW5XaXRoR29vZ2xlIiwic2lnbkluV2l0aE9BdXRoIiwicHJvdmlkZXIiLCJvcHRpb25zIiwicmVkaXJlY3RUbyIsIndpbmRvdyIsImxvY2F0aW9uIiwib3JpZ2luIiwiaXNMb2FkaW5nIiwiaXNBdXRoZW50aWNhdGVkIiwiaXNBcHByb3ZlZCIsImFwcHJvdmVkIiwibG9naW4iLCJtdXRhdGUiLCJsb2dvdXQiLCJpc0xvZ2luTG9hZGluZyIsImlzUGVuZGluZyIsImlzTG9nb3V0TG9hZGluZyIsInJlZmV0Y2giXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuth.ts\n"));

/***/ })

});