'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { apiClient } from '@/lib/api-client';
import { toast } from 'sonner';

export default function AuthCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get the auth code from URL params
        const code = searchParams.get('code');
        const error_param = searchParams.get('error');

        if (error_param) {
          console.error('OAuth error:', error_param);
          setError('Authentication was cancelled or failed');
          toast.error('Authentication failed', {
            description: 'Please try signing in again'
          });
          router.push('/login');
          return;
        }

        if (!code) {
          setError('No authorization code received');
          toast.error('Authentication failed', {
            description: 'No authorization code received'
          });
          router.push('/login');
          return;
        }

        // Exchange code for session via our API
        const response = await apiClient.post('/auth/callback', { code });

        if (response.user) {
          toast.success('Welcome!', {
            description: `Signed in as ${response.user.name}`
          });
          router.push('/dashboard');
        } else {
          setError('Authentication failed');
          toast.error('Authentication failed', {
            description: 'Please try again'
          });
          router.push('/login');
        }
      } catch (err) {
        console.error('Auth callback processing error:', err);
        setError('Authentication processing failed');
        toast.error('Authentication failed', {
          description: 'Please try again'
        });
        router.push('/login');
      } finally {
        setIsProcessing(false);
      }
    };

    handleAuthCallback();
  }, [searchParams, router]);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-lg font-semibold mb-2">Authentication Error</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => router.push('/login')}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Back to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        <p className="mt-4 text-gray-600">
          {isProcessing ? 'Completing sign in...' : 'Redirecting...'}
        </p>
      </div>
    </div>
  );
}