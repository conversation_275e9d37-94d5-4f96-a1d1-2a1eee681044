import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    // Get the origin for redirect
    const origin = request.headers.get('origin') || 'http://localhost:3000';
    
    // Create OAuth URL for Google
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${origin}/auth/callback`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    });

    if (error) {
      console.error('Google OAuth error:', error);
      return NextResponse.redirect(`${origin}/login?error=oauth_failed`);
    }

    if (data.url) {
      return NextResponse.redirect(data.url);
    }

    return NextResponse.redirect(`${origin}/login?error=no_oauth_url`);

  } catch (error) {
    console.error('Google OAuth setup error:', error);
    const origin = request.headers.get('origin') || 'http://localhost:3000';
    return NextResponse.redirect(`${origin}/login?error=server_error`);
  }
}
