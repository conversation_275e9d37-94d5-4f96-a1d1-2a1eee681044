import { createClient, RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { queryClient } from '@/lib/query-client';
import { queryKeys } from '@/lib/query-keys';
import { Task, TimerSession, Notification } from '@/types';
import { apiClient } from '@/lib/api-client';

export interface RealtimeSubscription {
  id: string;
  channel: RealtimeChannel;
  table: string;
  filter?: string;
  callback: (payload: RealtimePostgresChangesPayload<any>) => void;
}

export interface RealtimeStatus {
  isConnected: boolean;
  subscriptions: RealtimeSubscription[];
  lastHeartbeat: Date | null;
  reconnectAttempts: number;
}

export class RealtimeService {
  private static instance: RealtimeService;
  private supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
  
  private subscriptions = new Map<string, RealtimeSubscription>();
  private statusListeners = new Set<(status: RealtimeStatus) => void>();
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private lastHeartbeat: Date | null = null;

  static getInstance(): RealtimeService {
    if (!RealtimeService.instance) {
      RealtimeService.instance = new RealtimeService();
    }
    return RealtimeService.instance;
  }

  async initialize(userId: string): Promise<void> {
    console.log('[Realtime] Initializing realtime service for user:', userId);

    // Get subscription configuration from API
    try {
      const config = await apiClient.post('/realtime/subscribe', {
        tables: ['tasks', 'notifications', 'task_sessions', 'users']
      });
      this.subscriptionConfig = config.config;
    } catch (error) {
      console.error('[Realtime] Failed to get subscription config:', error);
      // Fallback to default config
      this.subscriptionConfig = {
        userId,
        tables: [
          { table: 'tasks', filter: `or(assigned_to.eq.${userId},assigned_by.eq.${userId})`, events: ['*'] },
          { table: 'notifications', filter: `user_id.eq.${userId}`, events: ['*'] },
          { table: 'task_sessions', filter: `user_id.eq.${userId}`, events: ['*'] },
          { table: 'users', filter: `id.eq.${userId}`, events: ['*'] }
        ],
        channelPrefix: `user_${userId}`
      };
    }

    // Set up connection monitoring
    this.setupConnectionMonitoring();

    // Subscribe to user-specific channels
    await this.subscribeToUserChannels(userId);

    console.log('[Realtime] Realtime service initialized');
  }

  private setupConnectionMonitoring(): void {
    // Monitor connection status
    this.supabase.realtime.onOpen(() => {
      console.log('[Realtime] Connection opened');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.notifyStatusListeners();
      this.startHeartbeat();
    });

    this.supabase.realtime.onClose(() => {
      console.log('[Realtime] Connection closed');
      this.isConnected = false;
      this.notifyStatusListeners();
      this.stopHeartbeat();
      this.handleReconnect();
    });

    this.supabase.realtime.onError((error) => {
      console.error('[Realtime] Connection error:', error);
      this.isConnected = false;
      this.notifyStatusListeners();
    });
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.lastHeartbeat = new Date();
      this.notifyStatusListeners();
    }, 30000); // 30 seconds
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  private async handleReconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('[Realtime] Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`[Realtime] Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    
    setTimeout(async () => {
      try {
        // Resubscribe to all channels
        for (const subscription of this.subscriptions.values()) {
          await this.resubscribe(subscription);
        }
      } catch (error) {
        console.error('[Realtime] Reconnection failed:', error);
        this.handleReconnect();
      }
    }, delay);
  }

  private async resubscribe(subscription: RealtimeSubscription): Promise<void> {
    try {
      // Remove old subscription
      await subscription.channel.unsubscribe();
      
      // Create new subscription
      const newChannel = this.supabase
        .channel(`${subscription.table}_${subscription.id}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: subscription.table,
            filter: subscription.filter,
          },
          subscription.callback
        )
        .subscribe();

      // Update subscription
      subscription.channel = newChannel;
      
      console.log(`[Realtime] Resubscribed to ${subscription.table}`);
    } catch (error) {
      console.error(`[Realtime] Failed to resubscribe to ${subscription.table}:`, error);
    }
  }

  async subscribeToUserChannels(userId: string): Promise<void> {
    // Subscribe to tasks assigned to or created by the user
    await this.subscribeToTasks(userId);
    
    // Subscribe to timer sessions for the user
    await this.subscribeToTimerSessions(userId);
    
    // Subscribe to notifications for the user
    await this.subscribeToNotifications(userId);
    
    // Subscribe to user-specific system events
    await this.subscribeToUserEvents(userId);
  }

  async subscribeToTasks(userId: string): Promise<void> {
    const subscriptionId = `tasks_${userId}`;
    
    if (this.subscriptions.has(subscriptionId)) {
      return; // Already subscribed
    }

    const channel = this.supabase
      .channel(`tasks_${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tasks',
          filter: `or(assigned_to.eq.${userId},assigned_by.eq.${userId})`,
        },
        (payload) => this.handleTaskChange(payload)
      )
      .subscribe();

    const subscription: RealtimeSubscription = {
      id: subscriptionId,
      channel,
      table: 'tasks',
      filter: `or(assigned_to.eq.${userId},assigned_by.eq.${userId})`,
      callback: (payload) => this.handleTaskChange(payload),
    };

    this.subscriptions.set(subscriptionId, subscription);
    console.log('[Realtime] Subscribed to tasks for user:', userId);
  }

  async subscribeToTimerSessions(userId: string): Promise<void> {
    const subscriptionId = `timer_sessions_${userId}`;
    
    if (this.subscriptions.has(subscriptionId)) {
      return;
    }

    const channel = this.supabase
      .channel(`timer_sessions_${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'task_sessions',
          filter: `user_id.eq.${userId}`,
        },
        (payload) => this.handleTimerSessionChange(payload)
      )
      .subscribe();

    const subscription: RealtimeSubscription = {
      id: subscriptionId,
      channel,
      table: 'task_sessions',
      filter: `user_id.eq.${userId}`,
      callback: (payload) => this.handleTimerSessionChange(payload),
    };

    this.subscriptions.set(subscriptionId, subscription);
    console.log('[Realtime] Subscribed to timer sessions for user:', userId);
  }

  async subscribeToNotifications(userId: string): Promise<void> {
    const subscriptionId = `notifications_${userId}`;
    
    if (this.subscriptions.has(subscriptionId)) {
      return;
    }

    const channel = this.supabase
      .channel(`notifications_${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications',
          filter: `user_id.eq.${userId}`,
        },
        (payload) => this.handleNotificationChange(payload)
      )
      .subscribe();

    const subscription: RealtimeSubscription = {
      id: subscriptionId,
      channel,
      table: 'notifications',
      filter: `user_id.eq.${userId}`,
      callback: (payload) => this.handleNotificationChange(payload),
    };

    this.subscriptions.set(subscriptionId, subscription);
    console.log('[Realtime] Subscribed to notifications for user:', userId);
  }

  async subscribeToUserEvents(userId: string): Promise<void> {
    const subscriptionId = `user_events_${userId}`;
    
    if (this.subscriptions.has(subscriptionId)) {
      return;
    }

    const channel = this.supabase
      .channel(`user_events_${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'users',
          filter: `id.eq.${userId}`,
        },
        (payload) => this.handleUserChange(payload)
      )
      .subscribe();

    const subscription: RealtimeSubscription = {
      id: subscriptionId,
      channel,
      table: 'users',
      filter: `id.eq.${userId}`,
      callback: (payload) => this.handleUserChange(payload),
    };

    this.subscriptions.set(subscriptionId, subscription);
    console.log('[Realtime] Subscribed to user events for user:', userId);
  }

  private handleTaskChange(payload: RealtimePostgresChangesPayload<Task>): void {
    console.log('[Realtime] Task change:', payload);
    
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    switch (eventType) {
      case 'INSERT':
        // Invalidate task lists to include new task
        queryClient.invalidateQueries({ queryKey: queryKeys.tasks.lists() });
        break;
        
      case 'UPDATE':
        if (newRecord) {
          // Update specific task cache
          queryClient.setQueryData(queryKeys.tasks.detail(newRecord.id), newRecord);
          
          // Invalidate task lists to reflect changes
          queryClient.invalidateQueries({ queryKey: queryKeys.tasks.lists() });
          
          // If task status changed, invalidate related queries
          if (oldRecord && oldRecord.status !== newRecord.status) {
            queryClient.invalidateQueries({ queryKey: queryKeys.timer.byTask(newRecord.id) });
          }
        }
        break;
        
      case 'DELETE':
        if (oldRecord) {
          // Remove from cache
          queryClient.removeQueries({ queryKey: queryKeys.tasks.detail(oldRecord.id) });
          
          // Invalidate task lists
          queryClient.invalidateQueries({ queryKey: queryKeys.tasks.lists() });
        }
        break;
    }
  }

  private handleTimerSessionChange(payload: RealtimePostgresChangesPayload<TimerSession>): void {
    console.log('[Realtime] Timer session change:', payload);
    
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    switch (eventType) {
      case 'INSERT':
        if (newRecord) {
          // Update active timer cache
          queryClient.setQueryData(queryKeys.timer.active(), newRecord);
          
          // Invalidate timer sessions
          queryClient.invalidateQueries({ queryKey: queryKeys.timer.sessions() });
        }
        break;
        
      case 'UPDATE':
        if (newRecord) {
          // Update specific session cache
          queryClient.setQueryData(queryKeys.timer.session(newRecord.id), newRecord);
          
          // Update active timer if this is the active session
          if (!newRecord.end_time) {
            queryClient.setQueryData(queryKeys.timer.active(), newRecord);
          } else {
            queryClient.setQueryData(queryKeys.timer.active(), null);
          }
          
          // Invalidate related queries
          queryClient.invalidateQueries({ queryKey: queryKeys.timer.sessions() });
          queryClient.invalidateQueries({ queryKey: queryKeys.timer.byTask(newRecord.task_id) });
        }
        break;
        
      case 'DELETE':
        if (oldRecord) {
          // Remove from cache
          queryClient.removeQueries({ queryKey: queryKeys.timer.session(oldRecord.id) });
          
          // Clear active timer if this was the active session
          queryClient.setQueryData(queryKeys.timer.active(), null);
          
          // Invalidate timer sessions
          queryClient.invalidateQueries({ queryKey: queryKeys.timer.sessions() });
        }
        break;
    }
  }

  private handleNotificationChange(payload: RealtimePostgresChangesPayload<Notification>): void {
    console.log('[Realtime] Notification change:', payload);
    
    const { eventType, new: newRecord } = payload;
    
    switch (eventType) {
      case 'INSERT':
        // Invalidate notification queries to show new notification
        queryClient.invalidateQueries({ queryKey: queryKeys.notifications.lists() });
        queryClient.invalidateQueries({ queryKey: queryKeys.notifications.unread() });
        queryClient.invalidateQueries({ queryKey: queryKeys.notifications.stats() });
        break;
        
      case 'UPDATE':
        if (newRecord) {
          // Update specific notification cache
          queryClient.setQueryData(queryKeys.notifications.detail(newRecord.id), newRecord);
          
          // Invalidate notification lists and stats
          queryClient.invalidateQueries({ queryKey: queryKeys.notifications.lists() });
          queryClient.invalidateQueries({ queryKey: queryKeys.notifications.unread() });
          queryClient.invalidateQueries({ queryKey: queryKeys.notifications.stats() });
        }
        break;
        
      case 'DELETE':
        // Invalidate all notification queries
        queryClient.invalidateQueries({ queryKey: queryKeys.notifications.all });
        break;
    }
  }

  private handleUserChange(payload: RealtimePostgresChangesPayload<any>): void {
    console.log('[Realtime] User change:', payload);
    
    const { eventType, new: newRecord } = payload;
    
    if (eventType === 'UPDATE' && newRecord) {
      // Update user cache
      queryClient.setQueryData(queryKeys.users.detail(newRecord.id), newRecord);
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.user() });
    }
  }

  addStatusListener(listener: (status: RealtimeStatus) => void): void {
    this.statusListeners.add(listener);
  }

  removeStatusListener(listener: (status: RealtimeStatus) => void): void {
    this.statusListeners.delete(listener);
  }

  private notifyStatusListeners(): void {
    const status: RealtimeStatus = {
      isConnected: this.isConnected,
      subscriptions: Array.from(this.subscriptions.values()),
      lastHeartbeat: this.lastHeartbeat,
      reconnectAttempts: this.reconnectAttempts,
    };

    this.statusListeners.forEach(listener => listener(status));
  }

  getStatus(): RealtimeStatus {
    return {
      isConnected: this.isConnected,
      subscriptions: Array.from(this.subscriptions.values()),
      lastHeartbeat: this.lastHeartbeat,
      reconnectAttempts: this.reconnectAttempts,
    };
  }

  async unsubscribeAll(): Promise<void> {
    console.log('[Realtime] Unsubscribing from all channels');
    
    for (const subscription of this.subscriptions.values()) {
      await subscription.channel.unsubscribe();
    }
    
    this.subscriptions.clear();
    this.stopHeartbeat();
    this.isConnected = false;
    this.notifyStatusListeners();
  }

  async unsubscribe(subscriptionId: string): Promise<void> {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      await subscription.channel.unsubscribe();
      this.subscriptions.delete(subscriptionId);
      console.log(`[Realtime] Unsubscribed from ${subscriptionId}`);
    }
  }
}

// Export singleton instance
export const realtimeService = RealtimeService.getInstance();
