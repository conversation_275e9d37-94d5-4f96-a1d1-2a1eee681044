import { NextRequest, NextResponse } from 'next/server';
import { validateAuth } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const authResult = await validateAuth(request);
    if (!authResult) {
      return NextResponse.json(
        { error: { code: 'UNAUTHORIZED', message: 'Authentication required' } },
        { status: 401 }
      );
    }

    const { tables } = await request.json();

    if (!tables || !Array.isArray(tables)) {
      return NextResponse.json(
        { error: { code: 'INVALID_REQUEST', message: 'Tables array is required' } },
        { status: 400 }
      );
    }

    // Validate allowed tables
    const allowedTables = ['tasks', 'notifications', 'task_sessions', 'users'];
    const invalidTables = tables.filter(table => !allowedTables.includes(table));
    
    if (invalidTables.length > 0) {
      return NextResponse.json(
        { error: { code: 'INVALID_TABLES', message: `Invalid tables: ${invalidTables.join(', ')}` } },
        { status: 400 }
      );
    }

    // Return subscription configuration for client
    const subscriptionConfig = {
      userId: authResult.user.id,
      tables: tables.map(table => ({
        table,
        filter: getTableFilter(table, authResult.user.id),
        events: ['INSERT', 'UPDATE', 'DELETE']
      })),
      channelPrefix: `user_${authResult.user.id}`,
    };

    return NextResponse.json({
      success: true,
      config: subscriptionConfig,
    });

  } catch (error) {
    console.error('Realtime subscribe error:', error);
    return NextResponse.json(
      { error: { code: 'INTERNAL_ERROR', message: 'Internal server error' } },
      { status: 500 }
    );
  }
}

function getTableFilter(table: string, userId: string): string {
  switch (table) {
    case 'tasks':
      return `or(assigned_to.eq.${userId},assigned_by.eq.${userId})`;
    case 'notifications':
    case 'task_sessions':
      return `user_id.eq.${userId}`;
    case 'users':
      return `id.eq.${userId}`;
    default:
      return `user_id.eq.${userId}`;
  }
}
