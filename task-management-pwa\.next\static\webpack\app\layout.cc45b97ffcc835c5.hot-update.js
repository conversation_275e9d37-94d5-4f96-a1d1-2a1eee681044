"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f2d168f969f0\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcQUlcXGNhbmRpZC10YXNrLW1nbXRcXHRhc2stbWFuYWdlbWVudC1wd2FcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImYyZDE2OGY5NjlmMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/query-client.ts":
/*!*********************************!*\
  !*** ./src/lib/query-client.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQueryClient: () => (/* binding */ createQueryClient),\n/* harmony export */   queryClient: () => (/* binding */ queryClient),\n/* harmony export */   queryClientUtils: () => (/* binding */ queryClientUtils),\n/* harmony export */   queryDevTools: () => (/* binding */ queryDevTools)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n\n\n/**\n * Default query options for consistent behavior across the app\n */ const defaultQueryOptions = {\n    queries: {\n        // Cache data for 5 minutes by default\n        staleTime: 5 * 60 * 1000,\n        // Keep data in cache for 10 minutes after component unmount\n        gcTime: 10 * 60 * 1000,\n        // Retry failed requests 3 times with exponential backoff\n        retry: (failureCount, error)=>{\n            // Don't retry on 4xx errors (client errors)\n            if ((error === null || error === void 0 ? void 0 : error.status) >= 400 && (error === null || error === void 0 ? void 0 : error.status) < 500) {\n                return false;\n            }\n            // Retry up to 3 times for other errors\n            return failureCount < 3;\n        },\n        // Retry delay with exponential backoff\n        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),\n        // Refetch on window focus for critical data\n        refetchOnWindowFocus: (query)=>{\n            // Only refetch critical queries on window focus\n            const criticalQueries = [\n                'auth',\n                'timer',\n                'notifications'\n            ];\n            return criticalQueries.some((key)=>query.queryKey.some((k)=>typeof k === 'string' && k.includes(key)));\n        },\n        // Refetch on reconnect\n        refetchOnReconnect: true,\n        // Don't refetch on mount if data is fresh\n        refetchOnMount: (query)=>{\n            // Refetch if data is older than 1 minute\n            return Date.now() - (query.state.dataUpdatedAt || 0) > 60 * 1000;\n        }\n    },\n    mutations: {\n        // Retry mutations once\n        retry: 1,\n        // Retry delay for mutations\n        retryDelay: 1000\n    }\n};\n/**\n * Query cache configuration with global error handling\n */ const queryCache = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.QueryCache({\n    onError: (error, query)=>{\n        console.error('Query error:', error, 'Query:', query.queryKey);\n        // Don't show toast for background refetches\n        if (query.state.fetchStatus === 'fetching' && query.state.data !== undefined) {\n            return;\n        }\n        // Show user-friendly error messages\n        const errorMessage = getErrorMessage(error);\n        sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error('Error', {\n            description: errorMessage\n        });\n    },\n    onSuccess: (data, query)=>{\n        // Log successful queries in development\n        if (true) {\n            console.log('Query success:', query.queryKey, data);\n        }\n    }\n});\n/**\n * Mutation cache configuration with global error handling\n */ const mutationCache = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.MutationCache({\n    onError: (error, variables, context, mutation)=>{\n        console.error('Mutation error:', error, 'Variables:', variables);\n        // Show user-friendly error messages for mutations\n        const errorMessage = getErrorMessage(error);\n        (0,sonner__WEBPACK_IMPORTED_MODULE_0__.toast)({\n            title: 'Operation Failed',\n            description: errorMessage,\n            variant: 'destructive'\n        });\n    },\n    onSuccess: (data, variables, context, mutation)=>{\n        // Log successful mutations in development\n        if (true) {\n            console.log('Mutation success:', mutation.options.mutationKey, data);\n        }\n    }\n});\n/**\n * Extract user-friendly error messages from API responses\n */ function getErrorMessage(error) {\n    var _error_response_data_error, _error_response_data, _error_response;\n    // Handle network errors\n    if (!navigator.onLine) {\n        return 'You appear to be offline. Please check your connection.';\n    }\n    // Handle API errors\n    if (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : (_error_response_data_error = _error_response_data.error) === null || _error_response_data_error === void 0 ? void 0 : _error_response_data_error.message) {\n        return error.response.data.error.message;\n    }\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        return error.message;\n    }\n    // Handle HTTP status codes\n    if (error === null || error === void 0 ? void 0 : error.status) {\n        switch(error.status){\n            case 401:\n                return 'You are not authorized. Please log in again.';\n            case 403:\n                return 'You do not have permission to perform this action.';\n            case 404:\n                return 'The requested resource was not found.';\n            case 429:\n                return 'Too many requests. Please try again later.';\n            case 500:\n                return 'Server error. Please try again later.';\n            case 503:\n                return 'Service temporarily unavailable. Please try again later.';\n            default:\n                return \"Request failed with status \".concat(error.status);\n        }\n    }\n    return 'An unexpected error occurred. Please try again.';\n}\n/**\n * Create and configure the query client\n */ function createQueryClient() {\n    return new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n        defaultOptions: defaultQueryOptions,\n        queryCache,\n        mutationCache\n    });\n}\n/**\n * Global query client instance\n */ const queryClient = createQueryClient();\n/**\n * Query client utilities for common operations\n */ const queryClientUtils = {\n    /**\n   * Invalidate queries by pattern\n   */ invalidateByPattern: async (pattern)=>{\n        await queryClient.invalidateQueries({\n            queryKey: pattern\n        });\n    },\n    /**\n   * Invalidate all queries for a specific entity\n   */ invalidateEntity: async (entity)=>{\n        await queryClient.invalidateQueries({\n            queryKey: [\n                entity\n            ]\n        });\n    },\n    /**\n   * Remove queries by pattern\n   */ removeByPattern: (pattern)=>{\n        queryClient.removeQueries({\n            queryKey: pattern\n        });\n    },\n    /**\n   * Set query data with optimistic updates\n   */ setOptimisticData: (queryKey, updater)=>{\n        queryClient.setQueryData(queryKey, updater);\n    },\n    /**\n   * Get cached query data\n   */ getCachedData: (queryKey)=>{\n        return queryClient.getQueryData(queryKey);\n    },\n    /**\n   * Prefetch query data\n   */ prefetch: async (queryKey, queryFn)=>{\n        await queryClient.prefetchQuery({\n            queryKey,\n            queryFn\n        });\n    },\n    /**\n   * Cancel outgoing queries\n   */ cancelQueries: async (queryKey)=>{\n        await queryClient.cancelQueries({\n            queryKey\n        });\n    },\n    /**\n   * Reset all queries and cache\n   */ resetAll: async ()=>{\n        await queryClient.resetQueries();\n    },\n    /**\n   * Clear all cache\n   */ clearCache: ()=>{\n        queryClient.clear();\n    },\n    /**\n   * Get query cache stats\n   */ getCacheStats: ()=>{\n        const cache = queryClient.getQueryCache();\n        const queries = cache.getAll();\n        return {\n            totalQueries: queries.length,\n            activeQueries: queries.filter((q)=>q.getObserversCount() > 0).length,\n            staleQueries: queries.filter((q)=>q.isStale()).length,\n            errorQueries: queries.filter((q)=>q.state.status === 'error').length,\n            loadingQueries: queries.filter((q)=>q.state.fetchStatus === 'fetching').length\n        };\n    }\n};\n/**\n * Development tools for debugging queries\n */ const queryDevTools = {\n    /**\n   * Log all queries and their states\n   */ logAllQueries: ()=>{\n        if (false) {}\n        const cache = queryClient.getQueryCache();\n        const queries = cache.getAll();\n        console.group('Query Cache State');\n        queries.forEach((query)=>{\n            console.log({\n                queryKey: query.queryKey,\n                status: query.state.status,\n                fetchStatus: query.state.fetchStatus,\n                dataUpdatedAt: new Date(query.state.dataUpdatedAt || 0),\n                error: query.state.error,\n                observers: query.getObserversCount()\n            });\n        });\n        console.groupEnd();\n    },\n    /**\n   * Monitor query performance\n   */ monitorPerformance: ()=>{\n        if (false) {}\n        const cache = queryClient.getQueryCache();\n        cache.subscribe((event)=>{\n            if (event.type === 'queryUpdated') {\n                var _query_state_fetchFailureReason;\n                const query = event.query;\n                const duration = Date.now() - ((_query_state_fetchFailureReason = query.state.fetchFailureReason) === null || _query_state_fetchFailureReason === void 0 ? void 0 : _query_state_fetchFailureReason.timestamp) || 0;\n                if (duration > 1000) {\n                    console.warn('Slow query detected:', {\n                        queryKey: query.queryKey,\n                        duration: \"\".concat(duration, \"ms\")\n                    });\n                }\n            }\n        });\n    },\n    /**\n   * Track cache hit/miss ratio\n   */ trackCacheHitRatio: ()=>{\n        if (false) {}\n        let hits = 0;\n        let misses = 0;\n        const cache = queryClient.getQueryCache();\n        cache.subscribe((event)=>{\n            if (event.type === 'queryUpdated') {\n                const query = event.query;\n                if (query.state.fetchStatus === 'fetching' && query.state.data !== undefined) {\n                    hits++;\n                } else if (query.state.fetchStatus === 'fetching') {\n                    misses++;\n                }\n                const total = hits + misses;\n                if (total > 0 && total % 10 === 0) {\n                    console.log(\"Cache hit ratio: \".concat((hits / total * 100).toFixed(1), \"% (\").concat(hits, \"/\").concat(total, \")\"));\n                }\n            }\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/query-client.ts\n"));

/***/ })

});