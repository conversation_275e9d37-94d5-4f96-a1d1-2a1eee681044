import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import { queryKeys } from '@/lib/config';
import { User, AuthResult } from '@/types';
import { toast } from 'sonner';

interface LoginResponse {
  user: User;
  session: {
    token: string;
    expiresAt: number;
  };
  approved: boolean;
}

export function useAuth() {
  const queryClient = useQueryClient();

  const authQuery = useQuery({
    queryKey: queryKeys.auth,
    queryFn: async (): Promise<AuthResult | null> => {
      try {
        const response = await apiClient.get<{ user: User | null; session: any }>('/auth/me');
        
        if (!response.user || !response.session) {
          return null;
        }

        return {
          user: response.user,
          session: response.session,
        };
      } catch (error) {
        console.error('Auth check failed:', error);
        return null;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false,
  });

  const loginMutation = useMutation({
    mutationFn: async (googleToken: string): Promise<LoginResponse> => {
      return apiClient.post<LoginResponse>('/auth/login', { googleToken });
    },
    onSuccess: (data) => {
      // Update the auth query cache
      queryClient.setQueryData(queryKeys.auth, {
        user: data.user,
        session: data.session,
      });
      
      // Session is handled by the API, no need for client-side session management
      toast.success('Login successful', {
        description: `Welcome back, ${data.user.name}!`
      });
    },
    onError: (error) => {
      console.error('Login failed:', error);
    },
  });

  const logoutMutation = useMutation({
    mutationFn: async (): Promise<void> => {
      await apiClient.post('/auth/logout');
    },
    onSuccess: () => {
      // Clear all cached data
      queryClient.clear();
      toast.success('Logged out successfully');
    },
    onError: (error) => {
      console.error('Logout failed:', error);
      // Still clear cache on error
      queryClient.clear();
      toast.error('Logout failed', {
        description: 'Please try again'
      });
    },
  });

  const signInWithGoogle = async () => {
    try {
      // Redirect to API endpoint for Google OAuth
      window.location.href = '/api/auth/google';
    } catch (error) {
      console.error('Google sign-in failed:', error);
      toast.error('Sign-in failed', {
        description: 'Please try again'
      });
      throw error;
    }
  };

  return {
    user: authQuery.data?.user || null,
    session: authQuery.data?.session || null,
    isLoading: authQuery.isLoading,
    isAuthenticated: !!authQuery.data?.user,
    isApproved: authQuery.data?.user?.approved || false,
    login: loginMutation.mutate,
    logout: logoutMutation.mutate,
    signInWithGoogle,
    isLoginLoading: loginMutation.isPending,
    isLogoutLoading: logoutMutation.isPending,
    refetch: authQuery.refetch,
  };
}