/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CAI%5Ccandid-task-mgmt%5Ctask-management-pwa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Ccandid-task-mgmt%5Ctask-management-pwa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CAI%5Ccandid-task-mgmt%5Ctask-management-pwa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Ccandid-task-mgmt%5Ctask-management-pwa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CAI%5Ccandid-task-mgmt%5Ctask-management-pwa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Ccandid-task-mgmt%5Ctask-management-pwa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cnotifications%5C%5CNotificationPopup.tsx%22%2C%22ids%22%3A%5B%22NotificationPopup%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cnotifications%5C%5CNotificationProvider.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CTimezoneProvider.tsx%22%2C%22ids%22%3A%5B%22TimezoneProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cnotifications%5C%5CNotificationPopup.tsx%22%2C%22ids%22%3A%5B%22NotificationPopup%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cnotifications%5C%5CNotificationProvider.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CTimezoneProvider.tsx%22%2C%22ids%22%3A%5B%22TimezoneProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notifications/NotificationPopup.tsx */ \"(rsc)/./src/components/notifications/NotificationPopup.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notifications/NotificationProvider.tsx */ \"(rsc)/./src/components/notifications/NotificationProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/query-provider.tsx */ \"(rsc)/./src/components/providers/query-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/TimezoneProvider.tsx */ \"(rsc)/./src/components/providers/TimezoneProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toast.tsx */ \"(rsc)/./src/components/ui/toast.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cnotifications%5C%5CNotificationPopup.tsx%22%2C%22ids%22%3A%5B%22NotificationPopup%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cnotifications%5C%5CNotificationProvider.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CTimezoneProvider.tsx%22%2C%22ids%22%3A%5B%22TimezoneProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBSSU1QyU1Q2NhbmRpZC10YXNrLW1nbXQlNUMlNUN0YXNrLW1hbmFnZW1lbnQtcHdhJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFzRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQUlcXFxcY2FuZGlkLXRhc2stbWdtdFxcXFx0YXNrLW1hbmFnZW1lbnQtcHdhXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxBSVxcY2FuZGlkLXRhc2stbWdtdFxcdGFzay1tYW5hZ2VtZW50LXB3YVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"03f52d364998\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcQUlcXGNhbmRpZC10YXNrLW1nbXRcXHRhc2stbWFuYWdlbWVudC1wd2FcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjAzZjUyZDM2NDk5OFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_query_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/query-provider */ \"(rsc)/./src/components/providers/query-provider.tsx\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/toast */ \"(rsc)/./src/components/ui/toast.tsx\");\n/* harmony import */ var _components_notifications_NotificationProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/notifications/NotificationProvider */ \"(rsc)/./src/components/notifications/NotificationProvider.tsx\");\n/* harmony import */ var _components_notifications_NotificationPopup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/notifications/NotificationPopup */ \"(rsc)/./src/components/notifications/NotificationPopup.tsx\");\n/* harmony import */ var _components_providers_TimezoneProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/providers/TimezoneProvider */ \"(rsc)/./src/components/providers/TimezoneProvider.tsx\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Task Management PWA\",\n    description: \"A mobile-first task management application for internal use\",\n    manifest: \"/manifest.json\",\n    appleWebApp: {\n        capable: true,\n        statusBarStyle: \"default\",\n        title: \"TaskMgmt\"\n    },\n    icons: {\n        icon: [\n            {\n                url: \"/icon-192x192.png\",\n                sizes: \"192x192\",\n                type: \"image/png\"\n            },\n            {\n                url: \"/icon-512x512.png\",\n                sizes: \"512x512\",\n                type: \"image/png\"\n            }\n        ],\n        apple: [\n            {\n                url: \"/icon-192x192.png\",\n                sizes: \"192x192\",\n                type: \"image/png\"\n            }\n        ]\n    }\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 1,\n    userScalable: false,\n    themeColor: \"#000000\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_7___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_8___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_query_provider__WEBPACK_IMPORTED_MODULE_2__.QueryProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_3__.ToastProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_TimezoneProvider__WEBPACK_IMPORTED_MODULE_6__.TimezoneProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notifications_NotificationProvider__WEBPACK_IMPORTED_MODULE_4__.NotificationProvider, {\n                            children: [\n                                children,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notifications_NotificationPopup__WEBPACK_IMPORTED_MODULE_5__.NotificationPopup, {}, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\AI\\candid-task-mgmt\\task-management-pwa\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/notifications/NotificationPopup.tsx":
/*!************************************************************!*\
  !*** ./src/components/notifications/NotificationPopup.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationPopup: () => (/* binding */ NotificationPopup)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const NotificationPopup = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotificationPopup() from the server but NotificationPopup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\AI\\candid-task-mgmt\\task-management-pwa\\src\\components\\notifications\\NotificationPopup.tsx",
"NotificationPopup",
);

/***/ }),

/***/ "(rsc)/./src/components/notifications/NotificationProvider.tsx":
/*!***************************************************************!*\
  !*** ./src/components/notifications/NotificationProvider.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),
/* harmony export */   useNotificationContext: () => (/* binding */ useNotificationContext)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const NotificationProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotificationProvider() from the server but NotificationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\AI\\candid-task-mgmt\\task-management-pwa\\src\\components\\notifications\\NotificationProvider.tsx",
"NotificationProvider",
);const useNotificationContext = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useNotificationContext() from the server but useNotificationContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\AI\\candid-task-mgmt\\task-management-pwa\\src\\components\\notifications\\NotificationProvider.tsx",
"useNotificationContext",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/TimezoneProvider.tsx":
/*!*******************************************************!*\
  !*** ./src/components/providers/TimezoneProvider.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TimezoneProvider: () => (/* binding */ TimezoneProvider),
/* harmony export */   triggerTimezoneChange: () => (/* binding */ triggerTimezoneChange),
/* harmony export */   useTimezone: () => (/* binding */ useTimezone)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const TimezoneProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call TimezoneProvider() from the server but TimezoneProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\AI\\candid-task-mgmt\\task-management-pwa\\src\\components\\providers\\TimezoneProvider.tsx",
"TimezoneProvider",
);const useTimezone = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTimezone() from the server but useTimezone is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\AI\\candid-task-mgmt\\task-management-pwa\\src\\components\\providers\\TimezoneProvider.tsx",
"useTimezone",
);const triggerTimezoneChange = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call triggerTimezoneChange() from the server but triggerTimezoneChange is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\AI\\candid-task-mgmt\\task-management-pwa\\src\\components\\providers\\TimezoneProvider.tsx",
"triggerTimezoneChange",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/query-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/query-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const QueryProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\AI\\candid-task-mgmt\\task-management-pwa\\src\\components\\providers\\query-provider.tsx",
"QueryProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),
/* harmony export */   useToast: () => (/* binding */ useToast)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const useToast = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useToast() from the server but useToast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\AI\\candid-task-mgmt\\task-management-pwa\\src\\components\\ui\\toast.tsx",
"useToast",
);const ToastProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\AI\\candid-task-mgmt\\task-management-pwa\\src\\components\\ui\\toast.tsx",
"ToastProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cnotifications%5C%5CNotificationPopup.tsx%22%2C%22ids%22%3A%5B%22NotificationPopup%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cnotifications%5C%5CNotificationProvider.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CTimezoneProvider.tsx%22%2C%22ids%22%3A%5B%22TimezoneProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cnotifications%5C%5CNotificationPopup.tsx%22%2C%22ids%22%3A%5B%22NotificationPopup%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cnotifications%5C%5CNotificationProvider.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CTimezoneProvider.tsx%22%2C%22ids%22%3A%5B%22TimezoneProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notifications/NotificationPopup.tsx */ \"(ssr)/./src/components/notifications/NotificationPopup.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notifications/NotificationProvider.tsx */ \"(ssr)/./src/components/notifications/NotificationProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/query-provider.tsx */ \"(ssr)/./src/components/providers/query-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/TimezoneProvider.tsx */ \"(ssr)/./src/components/providers/TimezoneProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toast.tsx */ \"(ssr)/./src/components/ui/toast.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cnotifications%5C%5CNotificationPopup.tsx%22%2C%22ids%22%3A%5B%22NotificationPopup%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cnotifications%5C%5CNotificationProvider.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CTimezoneProvider.tsx%22%2C%22ids%22%3A%5B%22TimezoneProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBSSU1QyU1Q2NhbmRpZC10YXNrLW1nbXQlNUMlNUN0YXNrLW1hbmFnZW1lbnQtcHdhJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFzRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQUlcXFxcY2FuZGlkLXRhc2stbWdtdFxcXFx0YXNrLW1hbmFnZW1lbnQtcHdhXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Ccandid-task-mgmt%5C%5Ctask-management-pwa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Home() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isAuthenticated, isApproved, isLoading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (!isLoading) {\n                if (!isAuthenticated) {\n                    router.push('/login');\n                } else if (!isApproved) {\n                    router.push('/waiting');\n                } else {\n                    router.push('/dashboard');\n                }\n            }\n        }\n    }[\"Home.useEffect\"], [\n        isAuthenticated,\n        isApproved,\n        isLoading,\n        router\n    ]);\n    // Show loading state while determining auth status\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-gray-600\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/notifications/NotificationPopup.tsx":
/*!************************************************************!*\
  !*** ./src/components/notifications/NotificationPopup.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationPopup: () => (/* binding */ NotificationPopup)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle,Pause,Play,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle,Pause,Play,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle,Pause,Play,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle,Pause,Play,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle,Pause,Play,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle,Pause,Play,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle,Pause,Play,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(ssr)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var _NotificationProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NotificationProvider */ \"(ssr)/./src/components/notifications/NotificationProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ NotificationPopup auto */ \n\n\n\n\n\n\n\nfunction NotificationPopup({ position = 'top-right', maxNotifications = 3, autoHideDuration = 5000 }) {\n    const { markAsRead } = (0,_NotificationProvider__WEBPACK_IMPORTED_MODULE_5__.useNotificationContext)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'task_assigned':\n            case 'task_created':\n                return _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case 'task_accepted':\n            case 'task_completed':\n                return _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            case 'timer_started':\n                return _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n            case 'timer_paused':\n            case 'timer_stopped':\n                return _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n            case 'task_overdue':\n            case 'timer_auto_stopped':\n                return _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n            case 'user_approved':\n                return _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            default:\n                return _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\n        }\n    };\n    const getNotificationColor = (type)=>{\n        switch(type){\n            case 'task_completed':\n            case 'user_approved':\n                return 'border-green-200 bg-green-50';\n            case 'task_overdue':\n            case 'timer_auto_stopped':\n                return 'border-red-200 bg-red-50';\n            case 'timer_started':\n            case 'task_assigned':\n                return 'border-blue-200 bg-blue-50';\n            case 'timer_paused':\n            case 'timer_stopped':\n                return 'border-yellow-200 bg-yellow-50';\n            default:\n                return 'border-gray-200 bg-white';\n        }\n    };\n    const getPositionClasses = ()=>{\n        switch(position){\n            case 'top-left':\n                return 'top-4 left-4';\n            case 'bottom-right':\n                return 'bottom-4 right-4';\n            case 'bottom-left':\n                return 'bottom-4 left-4';\n            case 'top-right':\n            default:\n                return 'top-4 right-4';\n        }\n    };\n    const removeNotification = (notificationId)=>{\n        setNotifications((prev)=>prev.filter((n)=>n.id !== notificationId));\n    };\n    const handleMarkAsRead = async (notification)=>{\n        await markAsRead(notification.id);\n        removeNotification(notification.id);\n    };\n    // Listen for new notifications\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationPopup.useEffect\": ()=>{\n            const handleNewNotification = {\n                \"NotificationPopup.useEffect.handleNewNotification\": (event)=>{\n                    const newNotification = event.detail;\n                    const popupNotification = {\n                        ...newNotification,\n                        showTime: Date.now()\n                    };\n                    setNotifications({\n                        \"NotificationPopup.useEffect.handleNewNotification\": (prev)=>{\n                            // Add new notification and limit to maxNotifications\n                            const updated = [\n                                popupNotification,\n                                ...prev\n                            ].slice(0, maxNotifications);\n                            return updated;\n                        }\n                    }[\"NotificationPopup.useEffect.handleNewNotification\"]);\n                }\n            }[\"NotificationPopup.useEffect.handleNewNotification\"];\n            window.addEventListener('notificationReceived', handleNewNotification);\n            return ({\n                \"NotificationPopup.useEffect\": ()=>window.removeEventListener('notificationReceived', handleNewNotification)\n            })[\"NotificationPopup.useEffect\"];\n        }\n    }[\"NotificationPopup.useEffect\"], [\n        maxNotifications\n    ]);\n    // Auto-hide notifications\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationPopup.useEffect\": ()=>{\n            if (autoHideDuration <= 0) return;\n            const interval = setInterval({\n                \"NotificationPopup.useEffect.interval\": ()=>{\n                    const now = Date.now();\n                    setNotifications({\n                        \"NotificationPopup.useEffect.interval\": (prev)=>prev.filter({\n                                \"NotificationPopup.useEffect.interval\": (notification)=>now - notification.showTime < autoHideDuration\n                            }[\"NotificationPopup.useEffect.interval\"])\n                    }[\"NotificationPopup.useEffect.interval\"]);\n                }\n            }[\"NotificationPopup.useEffect.interval\"], 1000);\n            return ({\n                \"NotificationPopup.useEffect\": ()=>clearInterval(interval)\n            })[\"NotificationPopup.useEffect\"];\n        }\n    }[\"NotificationPopup.useEffect\"], [\n        autoHideDuration\n    ]);\n    if (notifications.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `fixed ${getPositionClasses()} z-50 space-y-2 max-w-sm w-full`,\n        children: notifications.map((notification)=>{\n            const Icon = getNotificationIcon(notification.type);\n            const colorClasses = getNotificationColor(notification.type);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: `${colorClasses} shadow-lg border-l-4 animate-in slide-in-from-right duration-300`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-5 w-5 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"text-xs\",\n                                                    children: notification.type.replace('_', ' ')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_12__.formatDistanceToNow)(new Date(notification.created_at), {\n                                                        addSuffix: true\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-900 font-medium mb-2\",\n                                            children: notification.message\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>handleMarkAsRead(notification),\n                                                size: \"sm\",\n                                                variant: \"outline\",\n                                                className: \"h-7 text-xs\",\n                                                children: \"Mark Read\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>removeNotification(notification.id),\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"h-6 w-6 p-0 flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 15\n                        }, this),\n                        autoHideDuration > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 w-full bg-gray-200 rounded-full h-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-600 h-1 rounded-full transition-all duration-1000 ease-linear\",\n                                style: {\n                                    width: `${Math.max(0, 100 - (Date.now() - notification.showTime) / autoHideDuration * 100)}%`\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 17\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 13\n                }, this)\n            }, notification.id, false, {\n                fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n                lineNumber: 142,\n                columnNumber: 11\n            }, this);\n        })\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationPopup.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/notifications/NotificationPopup.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/notifications/NotificationProvider.tsx":
/*!***************************************************************!*\
  !*** ./src/components/notifications/NotificationProvider.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   useNotificationContext: () => (/* binding */ useNotificationContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle,Pause,Play,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle,Pause,Play,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle,Pause,Play,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle,Pause,Play,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle,Pause,Play,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle,Pause,Play,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* __next_internal_client_entry_do_not_use__ NotificationProvider,useNotificationContext auto */ \n\n\n\n\n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction NotificationProvider({ children }) {\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [recentNotifications, setRecentNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [enableToasts, setEnableToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"NotificationProvider.useState\": ()=>{\n            // Get toast preference from localStorage\n            if (false) {}\n            return true;\n        }\n    }[\"NotificationProvider.useState\"]);\n    // Save toast preference to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationProvider.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"NotificationProvider.useEffect\"], [\n        enableToasts\n    ]);\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'task_assigned':\n            case 'task_created':\n                return _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case 'task_accepted':\n            case 'task_completed':\n                return _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case 'timer_started':\n                return _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            case 'timer_paused':\n            case 'timer_stopped':\n                return _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n            case 'task_overdue':\n            case 'timer_auto_stopped':\n                return _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n            case 'user_approved':\n                return _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            default:\n                return _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_Pause_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n        }\n    };\n    const showToastNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[showToastNotification]\": (notification)=>{\n            if (!enableToasts) return;\n            const Icon = getNotificationIcon(notification.type);\n            // Determine toast variant based on notification type\n            let variant = 'default';\n            if (notification.type.includes('overdue') || notification.type.includes('auto_stopped')) {\n                variant = 'destructive';\n            }\n            toast({\n                title: 'New Notification',\n                description: notification.message,\n                variant,\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationProvider.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationProvider.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            });\n        }\n    }[\"NotificationProvider.useCallback[showToastNotification]\"], [\n        enableToasts,\n        toast\n    ]);\n    const fetchNotificationStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[fetchNotificationStats]\": async ()=>{\n            if (!user) return;\n            try {\n                const response = await fetch('/api/notifications/stats');\n                if (response.ok) {\n                    const data = await response.json();\n                    setUnreadCount(data.stats.unread_count);\n                    setRecentNotifications(data.recent_notifications || []);\n                }\n            } catch (error) {\n                console.error('Failed to fetch notification stats:', error);\n            }\n        }\n    }[\"NotificationProvider.useCallback[fetchNotificationStats]\"], [\n        user\n    ]);\n    const markAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[markAsRead]\": async (notificationId)=>{\n            try {\n                const response = await fetch('/api/notifications', {\n                    method: 'PATCH',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        notification_id: notificationId,\n                        action: 'mark_read'\n                    })\n                });\n                if (response.ok) {\n                    // Update local state\n                    setUnreadCount({\n                        \"NotificationProvider.useCallback[markAsRead]\": (prev)=>Math.max(0, prev - 1)\n                    }[\"NotificationProvider.useCallback[markAsRead]\"]);\n                    setRecentNotifications({\n                        \"NotificationProvider.useCallback[markAsRead]\": (prev)=>prev.map({\n                                \"NotificationProvider.useCallback[markAsRead]\": (notification)=>notification.id === notificationId ? {\n                                        ...notification,\n                                        read: true\n                                    } : notification\n                            }[\"NotificationProvider.useCallback[markAsRead]\"])\n                    }[\"NotificationProvider.useCallback[markAsRead]\"]);\n                }\n            } catch (error) {\n                console.error('Failed to mark notification as read:', error);\n            }\n        }\n    }[\"NotificationProvider.useCallback[markAsRead]\"], []);\n    const markAllAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[markAllAsRead]\": async ()=>{\n            try {\n                const response = await fetch('/api/notifications', {\n                    method: 'PATCH',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        notification_id: 'all',\n                        action: 'mark_all_read'\n                    })\n                });\n                if (response.ok) {\n                    setUnreadCount(0);\n                    setRecentNotifications({\n                        \"NotificationProvider.useCallback[markAllAsRead]\": (prev)=>prev.map({\n                                \"NotificationProvider.useCallback[markAllAsRead]\": (notification)=>({\n                                        ...notification,\n                                        read: true\n                                    })\n                            }[\"NotificationProvider.useCallback[markAllAsRead]\"])\n                    }[\"NotificationProvider.useCallback[markAllAsRead]\"]);\n                }\n            } catch (error) {\n                console.error('Failed to mark all notifications as read:', error);\n            }\n        }\n    }[\"NotificationProvider.useCallback[markAllAsRead]\"], []);\n    // Set up real-time subscription\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationProvider.useEffect\": ()=>{\n            if (!user) {\n                setIsConnected(false);\n                return;\n            }\n            let subscription;\n            const setupSubscription = {\n                \"NotificationProvider.useEffect.setupSubscription\": async ()=>{\n                    try {\n                        // Subscribe to notifications for the current user\n                        subscription = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.channel(`notifications:${user.id}`).on('postgres_changes', {\n                            event: 'INSERT',\n                            schema: 'public',\n                            table: 'notifications',\n                            filter: `user_id=eq.${user.id}`\n                        }, {\n                            \"NotificationProvider.useEffect.setupSubscription\": (payload)=>{\n                                const newNotification = payload.new;\n                                // Update unread count\n                                setUnreadCount({\n                                    \"NotificationProvider.useEffect.setupSubscription\": (prev)=>prev + 1\n                                }[\"NotificationProvider.useEffect.setupSubscription\"]);\n                                // Add to recent notifications\n                                setRecentNotifications({\n                                    \"NotificationProvider.useEffect.setupSubscription\": (prev)=>[\n                                            newNotification,\n                                            ...prev.slice(0, 4)\n                                        ]\n                                }[\"NotificationProvider.useEffect.setupSubscription\"]);\n                                // Show toast notification\n                                showToastNotification(newNotification);\n                                // Dispatch custom event for other components\n                                window.dispatchEvent(new CustomEvent('notificationReceived', {\n                                    detail: newNotification\n                                }));\n                            }\n                        }[\"NotificationProvider.useEffect.setupSubscription\"]).on('postgres_changes', {\n                            event: 'UPDATE',\n                            schema: 'public',\n                            table: 'notifications',\n                            filter: `user_id=eq.${user.id}`\n                        }, {\n                            \"NotificationProvider.useEffect.setupSubscription\": (payload)=>{\n                                const updatedNotification = payload.new;\n                                // Update recent notifications\n                                setRecentNotifications({\n                                    \"NotificationProvider.useEffect.setupSubscription\": (prev)=>prev.map({\n                                            \"NotificationProvider.useEffect.setupSubscription\": (notification)=>notification.id === updatedNotification.id ? updatedNotification : notification\n                                        }[\"NotificationProvider.useEffect.setupSubscription\"])\n                                }[\"NotificationProvider.useEffect.setupSubscription\"]);\n                                // If notification was marked as read, decrease unread count\n                                if (updatedNotification.read && !payload.old?.read) {\n                                    setUnreadCount({\n                                        \"NotificationProvider.useEffect.setupSubscription\": (prev)=>Math.max(0, prev - 1)\n                                    }[\"NotificationProvider.useEffect.setupSubscription\"]);\n                                }\n                            }\n                        }[\"NotificationProvider.useEffect.setupSubscription\"]).subscribe({\n                            \"NotificationProvider.useEffect.setupSubscription\": (status)=>{\n                                setIsConnected(status === 'SUBSCRIBED');\n                                if (status === 'SUBSCRIBED') {\n                                    console.log('Real-time notifications connected');\n                                } else if (status === 'CHANNEL_ERROR') {\n                                    console.error('Real-time notifications connection error');\n                                    setIsConnected(false);\n                                }\n                            }\n                        }[\"NotificationProvider.useEffect.setupSubscription\"]);\n                        // Initial fetch\n                        await fetchNotificationStats();\n                    } catch (error) {\n                        console.error('Failed to set up real-time notifications:', error);\n                        setIsConnected(false);\n                    }\n                }\n            }[\"NotificationProvider.useEffect.setupSubscription\"];\n            setupSubscription();\n            // Cleanup subscription on unmount\n            return ({\n                \"NotificationProvider.useEffect\": ()=>{\n                    if (subscription) {\n                        _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.removeChannel(subscription);\n                    }\n                }\n            })[\"NotificationProvider.useEffect\"];\n        }\n    }[\"NotificationProvider.useEffect\"], [\n        user,\n        showToastNotification,\n        fetchNotificationStats\n    ]);\n    // Periodic refresh as fallback\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationProvider.useEffect\": ()=>{\n            if (!user) return;\n            const interval = setInterval(fetchNotificationStats, 60000); // Every minute\n            return ({\n                \"NotificationProvider.useEffect\": ()=>clearInterval(interval)\n            })[\"NotificationProvider.useEffect\"];\n        }\n    }[\"NotificationProvider.useEffect\"], [\n        user,\n        fetchNotificationStats\n    ]);\n    const contextValue = {\n        unreadCount,\n        recentNotifications,\n        isConnected,\n        refreshNotifications: fetchNotificationStats,\n        markAsRead,\n        markAllAsRead,\n        enableToasts,\n        setEnableToasts\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\notifications\\\\NotificationProvider.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n}\nfunction useNotificationContext() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n    if (context === undefined) {\n        throw new Error('useNotificationContext must be used within a NotificationProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/notifications/NotificationProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/TimezoneProvider.tsx":
/*!*******************************************************!*\
  !*** ./src/components/providers/TimezoneProvider.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TimezoneProvider: () => (/* binding */ TimezoneProvider),\n/* harmony export */   triggerTimezoneChange: () => (/* binding */ triggerTimezoneChange),\n/* harmony export */   useTimezone: () => (/* binding */ useTimezone)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_timezone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/timezone */ \"(ssr)/./src/lib/timezone.ts\");\n/* __next_internal_client_entry_do_not_use__ TimezoneProvider,useTimezone,triggerTimezoneChange auto */ \n\n\nconst TimezoneContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction TimezoneProvider({ children }) {\n    const [timezone, setTimezone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('America/Chicago');\n    const [timezoneConfig, setTimezoneConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const refreshTimezone = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TimezoneProvider.useCallback[refreshTimezone]\": async ()=>{\n            try {\n                setIsLoading(true);\n                // Get current timezone\n                const currentTimezone = await _lib_timezone__WEBPACK_IMPORTED_MODULE_2__.timezoneService.getCurrentTimezone();\n                setTimezone(currentTimezone);\n                // Get full timezone configuration\n                const config = await _lib_timezone__WEBPACK_IMPORTED_MODULE_2__.timezoneService.getTimezoneConfig();\n                setTimezoneConfig(config);\n            } catch (error) {\n                console.error('Failed to refresh timezone:', error);\n                // Use default timezone on error\n                setTimezone('America/Chicago');\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"TimezoneProvider.useCallback[refreshTimezone]\"], []);\n    const formatDate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TimezoneProvider.useCallback[formatDate]\": async (date, options)=>{\n            return _lib_timezone__WEBPACK_IMPORTED_MODULE_2__.timezoneService.formatInAppTimezone(date, options);\n        }\n    }[\"TimezoneProvider.useCallback[formatDate]\"], []);\n    const convertToAppTimezone = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TimezoneProvider.useCallback[convertToAppTimezone]\": async (date)=>{\n            return _lib_timezone__WEBPACK_IMPORTED_MODULE_2__.timezoneService.convertToAppTimezone(date);\n        }\n    }[\"TimezoneProvider.useCallback[convertToAppTimezone]\"], []);\n    // Initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TimezoneProvider.useEffect\": ()=>{\n            refreshTimezone();\n        }\n    }[\"TimezoneProvider.useEffect\"], [\n        refreshTimezone\n    ]);\n    // Listen for timezone changes from other components\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TimezoneProvider.useEffect\": ()=>{\n            const handleTimezoneChange = {\n                \"TimezoneProvider.useEffect.handleTimezoneChange\": ()=>{\n                    refreshTimezone();\n                }\n            }[\"TimezoneProvider.useEffect.handleTimezoneChange\"];\n            window.addEventListener('timezoneChanged', handleTimezoneChange);\n            return ({\n                \"TimezoneProvider.useEffect\": ()=>window.removeEventListener('timezoneChanged', handleTimezoneChange)\n            })[\"TimezoneProvider.useEffect\"];\n        }\n    }[\"TimezoneProvider.useEffect\"], [\n        refreshTimezone\n    ]);\n    // Periodic refresh to catch timezone changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TimezoneProvider.useEffect\": ()=>{\n            const interval = setInterval(refreshTimezone, 5 * 60 * 1000); // Every 5 minutes\n            return ({\n                \"TimezoneProvider.useEffect\": ()=>clearInterval(interval)\n            })[\"TimezoneProvider.useEffect\"];\n        }\n    }[\"TimezoneProvider.useEffect\"], [\n        refreshTimezone\n    ]);\n    const contextValue = {\n        timezone,\n        timezoneConfig,\n        isLoading,\n        refreshTimezone,\n        formatDate,\n        convertToAppTimezone\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimezoneContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\providers\\\\TimezoneProvider.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\nfunction useTimezone() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TimezoneContext);\n    if (context === undefined) {\n        throw new Error('useTimezone must be used within a TimezoneProvider');\n    }\n    return context;\n}\n// Utility function to trigger timezone change events\nfunction triggerTimezoneChange() {\n    window.dispatchEvent(new CustomEvent('timezoneChanged'));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/TimezoneProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/query-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/query-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_query_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/query-client */ \"(ssr)/./src/lib/query-client.ts\");\n/* __next_internal_client_entry_do_not_use__ QueryProvider auto */ \n\n\n\n\nfunction QueryProvider({ children }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QueryProvider.useEffect\": ()=>{\n            // Initialize development tools in development mode\n            if (true) {\n                _lib_query_client__WEBPACK_IMPORTED_MODULE_2__.queryDevTools.monitorPerformance();\n                _lib_query_client__WEBPACK_IMPORTED_MODULE_2__.queryDevTools.trackCacheHitRatio();\n                // Log cache stats every 30 seconds in development\n                const interval = setInterval({\n                    \"QueryProvider.useEffect.interval\": ()=>{\n                        _lib_query_client__WEBPACK_IMPORTED_MODULE_2__.queryDevTools.logAllQueries();\n                    }\n                }[\"QueryProvider.useEffect.interval\"], 30000);\n                return ({\n                    \"QueryProvider.useEffect\": ()=>clearInterval(interval)\n                })[\"QueryProvider.useEffect\"];\n            }\n        }\n    }[\"QueryProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: _lib_query_client__WEBPACK_IMPORTED_MODULE_2__.queryClient,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__.ReactQueryDevtools, {\n                initialIsOpen: false,\n                buttonPosition: \"bottom-left\",\n                position: \"left\"\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\providers\\\\query-provider.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\providers\\\\query-provider.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/query-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n            destructive: \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"span\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"badge\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4QiwrYkFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FDRTtZQUNGQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUNFO1lBQ0ZDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBR0YsU0FBU0ssT0FBTyxFQUNkQyxTQUFTLEVBQ1RiLE9BQU8sRUFDUE8sSUFBSSxFQUNKTyxVQUFVLEtBQUssRUFDZixHQUFHQyxPQUlGO0lBQ0QsTUFBTUMsT0FBT0YsVUFBVW5CLHNEQUFJQSxHQUFHO0lBRTlCLHFCQUNFLDhEQUFDcUI7UUFDQ0MsYUFBVTtRQUNWSixXQUFXaEIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU087WUFBTU07UUFBVTtRQUN2RCxHQUFHRSxLQUFLOzs7Ozs7QUFHZjtBQUVpQyIsInNvdXJjZXMiOlsiRDpcXEFJXFxjYW5kaWQtdGFzay1tZ210XFx0YXNrLW1hbmFnZW1lbnQtcHdhXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxidXR0b24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBTbG90IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zbG90XCJcbmltcG9ydCB7IGN2YSwgdHlwZSBWYXJpYW50UHJvcHMgfSBmcm9tIFwiY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBidXR0b25WYXJpYW50cyA9IGN2YShcbiAgXCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTIgd2hpdGVzcGFjZS1ub3dyYXAgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGRpc2FibGVkOnBvaW50ZXItZXZlbnRzLW5vbmUgZGlzYWJsZWQ6b3BhY2l0eS01MCBbJl9zdmddOnBvaW50ZXItZXZlbnRzLW5vbmUgWyZfc3ZnOm5vdChbY2xhc3MqPSdzaXplLSddKV06c2l6ZS00IHNocmluay0wIFsmX3N2Z106c2hyaW5rLTAgb3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6Ym9yZGVyLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcvNTAgZm9jdXMtdmlzaWJsZTpyaW5nLVszcHhdIGFyaWEtaW52YWxpZDpyaW5nLWRlc3RydWN0aXZlLzIwIGRhcms6YXJpYS1pbnZhbGlkOnJpbmctZGVzdHJ1Y3RpdmUvNDAgYXJpYS1pbnZhbGlkOmJvcmRlci1kZXN0cnVjdGl2ZVwiLFxuICB7XG4gICAgdmFyaWFudHM6IHtcbiAgICAgIHZhcmlhbnQ6IHtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICBcImJnLXByaW1hcnkgdGV4dC1wcmltYXJ5LWZvcmVncm91bmQgc2hhZG93LXhzIGhvdmVyOmJnLXByaW1hcnkvOTBcIixcbiAgICAgICAgZGVzdHJ1Y3RpdmU6XG4gICAgICAgICAgXCJiZy1kZXN0cnVjdGl2ZSB0ZXh0LXdoaXRlIHNoYWRvdy14cyBob3ZlcjpiZy1kZXN0cnVjdGl2ZS85MCBmb2N1cy12aXNpYmxlOnJpbmctZGVzdHJ1Y3RpdmUvMjAgZGFyazpmb2N1cy12aXNpYmxlOnJpbmctZGVzdHJ1Y3RpdmUvNDAgZGFyazpiZy1kZXN0cnVjdGl2ZS82MFwiLFxuICAgICAgICBvdXRsaW5lOlxuICAgICAgICAgIFwiYm9yZGVyIGJnLWJhY2tncm91bmQgc2hhZG93LXhzIGhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGRhcms6YmctaW5wdXQvMzAgZGFyazpib3JkZXItaW5wdXQgZGFyazpob3ZlcjpiZy1pbnB1dC81MFwiLFxuICAgICAgICBzZWNvbmRhcnk6XG4gICAgICAgICAgXCJiZy1zZWNvbmRhcnkgdGV4dC1zZWNvbmRhcnktZm9yZWdyb3VuZCBzaGFkb3cteHMgaG92ZXI6Ymctc2Vjb25kYXJ5LzgwXCIsXG4gICAgICAgIGdob3N0OlxuICAgICAgICAgIFwiaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmQgZGFyazpob3ZlcjpiZy1hY2NlbnQvNTBcIixcbiAgICAgICAgbGluazogXCJ0ZXh0LXByaW1hcnkgdW5kZXJsaW5lLW9mZnNldC00IGhvdmVyOnVuZGVybGluZVwiLFxuICAgICAgfSxcbiAgICAgIHNpemU6IHtcbiAgICAgICAgZGVmYXVsdDogXCJoLTkgcHgtNCBweS0yIGhhcy1bPnN2Z106cHgtM1wiLFxuICAgICAgICBzbTogXCJoLTggcm91bmRlZC1tZCBnYXAtMS41IHB4LTMgaGFzLVs+c3ZnXTpweC0yLjVcIixcbiAgICAgICAgbGc6IFwiaC0xMCByb3VuZGVkLW1kIHB4LTYgaGFzLVs+c3ZnXTpweC00XCIsXG4gICAgICAgIGljb246IFwic2l6ZS05XCIsXG4gICAgICB9LFxuICAgIH0sXG4gICAgZGVmYXVsdFZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcbiAgICAgIHNpemU6IFwiZGVmYXVsdFwiLFxuICAgIH0sXG4gIH1cbilcblxuZnVuY3Rpb24gQnV0dG9uKHtcbiAgY2xhc3NOYW1lLFxuICB2YXJpYW50LFxuICBzaXplLFxuICBhc0NoaWxkID0gZmFsc2UsXG4gIC4uLnByb3BzXG59OiBSZWFjdC5Db21wb25lbnRQcm9wczxcImJ1dHRvblwiPiAmXG4gIFZhcmlhbnRQcm9wczx0eXBlb2YgYnV0dG9uVmFyaWFudHM+ICYge1xuICAgIGFzQ2hpbGQ/OiBib29sZWFuXG4gIH0pIHtcbiAgY29uc3QgQ29tcCA9IGFzQ2hpbGQgPyBTbG90IDogXCJidXR0b25cIlxuXG4gIHJldHVybiAoXG4gICAgPENvbXBcbiAgICAgIGRhdGEtc2xvdD1cImJ1dHRvblwiXG4gICAgICBjbGFzc05hbWU9e2NuKGJ1dHRvblZhcmlhbnRzKHsgdmFyaWFudCwgc2l6ZSwgY2xhc3NOYW1lIH0pKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCB7IEJ1dHRvbiwgYnV0dG9uVmFyaWFudHMgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2xvdCIsImN2YSIsImNuIiwiYnV0dG9uVmFyaWFudHMiLCJ2YXJpYW50cyIsInZhcmlhbnQiLCJkZWZhdWx0IiwiZGVzdHJ1Y3RpdmUiLCJvdXRsaW5lIiwic2Vjb25kYXJ5IiwiZ2hvc3QiLCJsaW5rIiwic2l6ZSIsInNtIiwibGciLCJpY29uIiwiZGVmYXVsdFZhcmlhbnRzIiwiQnV0dG9uIiwiY2xhc3NOYW1lIiwiYXNDaGlsZCIsInByb3BzIiwiQ29tcCIsImRhdGEtc2xvdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ useToast,ToastProvider auto */ \n\n\nlet toastContext = null;\nfunction useToast() {\n    if (!toastContext) {\n        throw new Error('useToast must be used within a ToastProvider');\n    }\n    return toastContext;\n}\nfunction ToastProvider({ children }) {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showToast = (message, type, duration = 5000)=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        const toast = {\n            id,\n            message,\n            type,\n            duration\n        };\n        setToasts((prev)=>[\n                ...prev,\n                toast\n            ]);\n        if (duration > 0) {\n            setTimeout(()=>{\n                removeToast(id);\n            }, duration);\n        }\n    };\n    const removeToast = (id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    };\n    // Set up the context\n    toastContext = {\n        showToast\n    };\n    const getIcon = (type)=>{\n        switch(type){\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, this);\n            case 'info':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-5 w-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getBackgroundColor = (type)=>{\n        switch(type){\n            case 'success':\n                return 'bg-green-50 border-green-200';\n            case 'error':\n                return 'bg-red-50 border-red-200';\n            case 'info':\n                return 'bg-blue-50 border-blue-200';\n            default:\n                return 'bg-gray-50 border-gray-200';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 right-4 z-50 space-y-2\",\n                children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n              flex items-center gap-3 p-4 rounded-lg border shadow-lg max-w-sm\n              ${getBackgroundColor(toast.type)}\n              animate-in slide-in-from-right duration-300\n            `,\n                        children: [\n                            getIcon(toast.type),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"flex-1 text-sm font-medium text-gray-900\",\n                                children: toast.message\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>removeToast(toast.id),\n                                className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, toast.id, true, {\n                        fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\candid-task-mgmt\\\\task-management-pwa\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-toast.ts":
/*!********************************!*\
  !*** ./src/hooks/use-toast.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./src/components/ui/toast.tsx\");\n\nfunction useToast() {\n    const { showToast } = (0,_components_ui_toast__WEBPACK_IMPORTED_MODULE_0__.useToast)();\n    const toast = ({ title, description, variant = 'default' })=>{\n        const message = description ? `${title}: ${description}` : title;\n        const type = variant === 'destructive' ? 'error' : 'success';\n        showToast(message, type);\n    };\n    return {\n        toast\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlLXRvYXN0LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9FO0FBUTdELFNBQVNBO0lBQ2QsTUFBTSxFQUFFRSxTQUFTLEVBQUUsR0FBR0QsOERBQWVBO0lBRXJDLE1BQU1FLFFBQVEsQ0FBQyxFQUFFQyxLQUFLLEVBQUVDLFdBQVcsRUFBRUMsVUFBVSxTQUFTLEVBQVM7UUFDL0QsTUFBTUMsVUFBVUYsY0FBYyxHQUFHRCxNQUFNLEVBQUUsRUFBRUMsYUFBYSxHQUFHRDtRQUMzRCxNQUFNSSxPQUFPRixZQUFZLGdCQUFnQixVQUFVO1FBQ25ESixVQUFVSyxTQUFTQztJQUNyQjtJQUVBLE9BQU87UUFBRUw7SUFBTTtBQUNqQiIsInNvdXJjZXMiOlsiRDpcXEFJXFxjYW5kaWQtdGFzay1tZ210XFx0YXNrLW1hbmFnZW1lbnQtcHdhXFxzcmNcXGhvb2tzXFx1c2UtdG9hc3QudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlVG9hc3QgYXMgdXNlVG9hc3RDb250ZXh0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RvYXN0JztcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVG9hc3Qge1xyXG4gIHRpdGxlOiBzdHJpbmc7XHJcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XHJcbiAgdmFyaWFudD86ICdkZWZhdWx0JyB8ICdkZXN0cnVjdGl2ZSc7XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiB1c2VUb2FzdCgpIHtcclxuICBjb25zdCB7IHNob3dUb2FzdCB9ID0gdXNlVG9hc3RDb250ZXh0KCk7XHJcblxyXG4gIGNvbnN0IHRvYXN0ID0gKHsgdGl0bGUsIGRlc2NyaXB0aW9uLCB2YXJpYW50ID0gJ2RlZmF1bHQnIH06IFRvYXN0KSA9PiB7XHJcbiAgICBjb25zdCBtZXNzYWdlID0gZGVzY3JpcHRpb24gPyBgJHt0aXRsZX06ICR7ZGVzY3JpcHRpb259YCA6IHRpdGxlO1xyXG4gICAgY29uc3QgdHlwZSA9IHZhcmlhbnQgPT09ICdkZXN0cnVjdGl2ZScgPyAnZXJyb3InIDogJ3N1Y2Nlc3MnO1xyXG4gICAgc2hvd1RvYXN0KG1lc3NhZ2UsIHR5cGUpO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiB7IHRvYXN0IH07XHJcbn0iXSwibmFtZXMiOlsidXNlVG9hc3QiLCJ1c2VUb2FzdENvbnRleHQiLCJzaG93VG9hc3QiLCJ0b2FzdCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ2YXJpYW50IiwibWVzc2FnZSIsInR5cGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./src/lib/api-client.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n\n\n\n\nfunction useAuth() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const authQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: _lib_config__WEBPACK_IMPORTED_MODULE_2__.queryKeys.auth,\n        queryFn: {\n            \"useAuth.useQuery[authQuery]\": async ()=>{\n                try {\n                    const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/auth/me');\n                    if (!response.user || !response.session) {\n                        return null;\n                    }\n                    return {\n                        user: response.user,\n                        session: response.session\n                    };\n                } catch (error) {\n                    console.error('Auth check failed:', error);\n                    return null;\n                }\n            }\n        }[\"useAuth.useQuery[authQuery]\"],\n        staleTime: 5 * 60 * 1000,\n        retry: false\n    });\n    const loginMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: {\n            \"useAuth.useMutation[loginMutation]\": async (googleToken)=>{\n                return _lib_api_client__WEBPACK_IMPORTED_MODULE_1__.apiClient.post('/auth/login', {\n                    googleToken\n                });\n            }\n        }[\"useAuth.useMutation[loginMutation]\"],\n        onSuccess: {\n            \"useAuth.useMutation[loginMutation]\": (data)=>{\n                // Update the auth query cache\n                queryClient.setQueryData(_lib_config__WEBPACK_IMPORTED_MODULE_2__.queryKeys.auth, {\n                    user: data.user,\n                    session: data.session\n                });\n                // Set the session in Supabase client\n                if (data.session.token) {\n                    const expiresAt = data.session.expiresAt;\n                    const expiresIn = expiresAt - Math.floor(Date.now() / 1000);\n                    _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.setSession({\n                        access_token: data.session.token,\n                        refresh_token: '',\n                        expires_in: expiresIn,\n                        token_type: 'bearer',\n                        user: data.user\n                    });\n                }\n            }\n        }[\"useAuth.useMutation[loginMutation]\"],\n        onError: {\n            \"useAuth.useMutation[loginMutation]\": (error)=>{\n                console.error('Login failed:', error);\n            }\n        }[\"useAuth.useMutation[loginMutation]\"]\n    });\n    const logoutMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: {\n            \"useAuth.useMutation[logoutMutation]\": async ()=>{\n                await _lib_api_client__WEBPACK_IMPORTED_MODULE_1__.apiClient.post('/auth/logout');\n            }\n        }[\"useAuth.useMutation[logoutMutation]\"],\n        onSuccess: {\n            \"useAuth.useMutation[logoutMutation]\": ()=>{\n                // Clear all cached data\n                queryClient.clear();\n                // Sign out from Supabase client\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n            }\n        }[\"useAuth.useMutation[logoutMutation]\"],\n        onError: {\n            \"useAuth.useMutation[logoutMutation]\": (error)=>{\n                console.error('Logout failed:', error);\n                // Still clear cache and sign out on error\n                queryClient.clear();\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n            }\n        }[\"useAuth.useMutation[logoutMutation]\"]\n    });\n    const signInWithGoogle = async ()=>{\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithOAuth({\n                provider: 'google',\n                options: {\n                    redirectTo: `${window.location.origin}/auth/callback`\n                }\n            });\n            if (error) {\n                throw error;\n            }\n            return data;\n        } catch (error) {\n            console.error('Google sign-in failed:', error);\n            throw error;\n        }\n    };\n    return {\n        user: authQuery.data?.user || null,\n        session: authQuery.data?.session || null,\n        isLoading: authQuery.isLoading,\n        isAuthenticated: !!authQuery.data?.user,\n        isApproved: authQuery.data?.user?.approved || false,\n        login: loginMutation.mutate,\n        logout: logoutMutation.mutate,\n        signInWithGoogle,\n        isLoginLoading: loginMutation.isPending,\n        isLogoutLoading: logoutMutation.isPending,\n        refetch: authQuery.refetch\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api-client.ts":
/*!*******************************!*\
  !*** ./src/lib/api-client.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/config.ts\");\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\n\nclass APIError extends Error {\n    constructor(message, code, status){\n        super(message);\n        this.name = 'APIError';\n        this.code = code;\n        this.status = status;\n    }\n}\nclass APIClient {\n    constructor(){\n        this.baseUrl = (0,_config__WEBPACK_IMPORTED_MODULE_0__.getConfig)().apiUrl;\n    }\n    async getAuthHeaders() {\n        const { data: { session } } = await _supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.getSession();\n        if (session?.access_token) {\n            return {\n                'Authorization': `Bearer ${session.access_token}`\n            };\n        }\n        return {};\n    }\n    async request(endpoint, options = {}) {\n        const url = `${this.baseUrl}${endpoint}`;\n        const authHeaders = await this.getAuthHeaders();\n        const config = {\n            headers: {\n                'Content-Type': 'application/json',\n                ...authHeaders,\n                ...options.headers\n            },\n            ...options\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new APIError(errorData.error?.message || 'An error occurred', errorData.error?.code || 'UNKNOWN_ERROR', response.status);\n            }\n            return response.json();\n        } catch (error) {\n            if (error instanceof APIError) {\n                throw error;\n            }\n            // Network or other errors\n            throw new APIError('Network error occurred', 'NETWORK_ERROR', 0);\n        }\n    }\n    async get(endpoint, params) {\n        const url = new URL(`${this.baseUrl}${endpoint}`);\n        if (params) {\n            Object.entries(params).forEach(([key, value])=>{\n                if (value !== undefined && value !== null) {\n                    url.searchParams.append(key, String(value));\n                }\n            });\n        }\n        return this.request(url.pathname + url.search);\n    }\n    async post(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'POST',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async put(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async delete(endpoint) {\n        return this.request(endpoint, {\n            method: 'DELETE'\n        });\n    }\n}\nconst apiClient = new APIClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api-client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_TIMEZONE: () => (/* binding */ DEFAULT_TIMEZONE),\n/* harmony export */   TIMER_CONFIG: () => (/* binding */ TIMER_CONFIG),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   getConfig: () => (/* binding */ getConfig),\n/* harmony export */   queryKeys: () => (/* binding */ queryKeys)\n/* harmony export */ });\n// Application configuration\nconst config = {\n    development: {\n        apiUrl: 'http://localhost:3000/api',\n        supabaseUrl: \"https://placeholder.supabase.co\",\n        domain: 'localhost:3000'\n    },\n    production: {\n        apiUrl: 'https://your-domain.com/api',\n        supabaseUrl: \"https://placeholder.supabase.co\",\n        domain: 'your-domain.com'\n    }\n};\nconst getConfig = ()=>{\n    return config[\"development\"] || config.development;\n};\n// Default timezone configuration\nconst DEFAULT_TIMEZONE = 'America/Chicago';\n// Timer configuration\nconst TIMER_CONFIG = {\n    maxDurationHours: 6,\n    updateIntervalMs: 1000\n};\n// Query keys for TanStack Query\nconst queryKeys = {\n    auth: [\n        'auth'\n    ],\n    user: (id)=>[\n            'user',\n            id\n        ],\n    users: (filters)=>[\n            'users',\n            filters\n        ],\n    tasks: (filters)=>[\n            'tasks',\n            filters\n        ],\n    task: (id)=>[\n            'task',\n            id\n        ],\n    timer: [\n        'timer'\n    ],\n    reports: (params)=>[\n            'reports',\n            params\n        ],\n    notifications: [\n        'notifications'\n    ],\n    notificationStats: [\n        'notifications',\n        'stats'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/config.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/query-client.ts":
/*!*********************************!*\
  !*** ./src/lib/query-client.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQueryClient: () => (/* binding */ createQueryClient),\n/* harmony export */   queryClient: () => (/* binding */ queryClient),\n/* harmony export */   queryClientUtils: () => (/* binding */ queryClientUtils),\n/* harmony export */   queryDevTools: () => (/* binding */ queryDevTools)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./src/hooks/use-toast.ts\");\n\n\n/**\n * Default query options for consistent behavior across the app\n */ const defaultQueryOptions = {\n    queries: {\n        // Cache data for 5 minutes by default\n        staleTime: 5 * 60 * 1000,\n        // Keep data in cache for 10 minutes after component unmount\n        gcTime: 10 * 60 * 1000,\n        // Retry failed requests 3 times with exponential backoff\n        retry: (failureCount, error)=>{\n            // Don't retry on 4xx errors (client errors)\n            if (error?.status >= 400 && error?.status < 500) {\n                return false;\n            }\n            // Retry up to 3 times for other errors\n            return failureCount < 3;\n        },\n        // Retry delay with exponential backoff\n        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),\n        // Refetch on window focus for critical data\n        refetchOnWindowFocus: (query)=>{\n            // Only refetch critical queries on window focus\n            const criticalQueries = [\n                'auth',\n                'timer',\n                'notifications'\n            ];\n            return criticalQueries.some((key)=>query.queryKey.some((k)=>typeof k === 'string' && k.includes(key)));\n        },\n        // Refetch on reconnect\n        refetchOnReconnect: true,\n        // Don't refetch on mount if data is fresh\n        refetchOnMount: (query)=>{\n            // Refetch if data is older than 1 minute\n            return Date.now() - (query.state.dataUpdatedAt || 0) > 60 * 1000;\n        }\n    },\n    mutations: {\n        // Retry mutations once\n        retry: 1,\n        // Retry delay for mutations\n        retryDelay: 1000\n    }\n};\n/**\n * Query cache configuration with global error handling\n */ const queryCache = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.QueryCache({\n    onError: (error, query)=>{\n        console.error('Query error:', error, 'Query:', query.queryKey);\n        // Don't show toast for background refetches\n        if (query.state.fetchStatus === 'fetching' && query.state.data !== undefined) {\n            return;\n        }\n        // Show user-friendly error messages\n        const errorMessage = getErrorMessage(error);\n        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_0__.toast)({\n            title: 'Error',\n            description: errorMessage,\n            variant: 'destructive'\n        });\n    },\n    onSuccess: (data, query)=>{\n        // Log successful queries in development\n        if (true) {\n            console.log('Query success:', query.queryKey, data);\n        }\n    }\n});\n/**\n * Mutation cache configuration with global error handling\n */ const mutationCache = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.MutationCache({\n    onError: (error, variables, context, mutation)=>{\n        console.error('Mutation error:', error, 'Variables:', variables);\n        // Show user-friendly error messages for mutations\n        const errorMessage = getErrorMessage(error);\n        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_0__.toast)({\n            title: 'Operation Failed',\n            description: errorMessage,\n            variant: 'destructive'\n        });\n    },\n    onSuccess: (data, variables, context, mutation)=>{\n        // Log successful mutations in development\n        if (true) {\n            console.log('Mutation success:', mutation.options.mutationKey, data);\n        }\n    }\n});\n/**\n * Extract user-friendly error messages from API responses\n */ function getErrorMessage(error) {\n    // Handle network errors\n    if (!navigator.onLine) {\n        return 'You appear to be offline. Please check your connection.';\n    }\n    // Handle API errors\n    if (error?.response?.data?.error?.message) {\n        return error.response.data.error.message;\n    }\n    if (error?.message) {\n        return error.message;\n    }\n    // Handle HTTP status codes\n    if (error?.status) {\n        switch(error.status){\n            case 401:\n                return 'You are not authorized. Please log in again.';\n            case 403:\n                return 'You do not have permission to perform this action.';\n            case 404:\n                return 'The requested resource was not found.';\n            case 429:\n                return 'Too many requests. Please try again later.';\n            case 500:\n                return 'Server error. Please try again later.';\n            case 503:\n                return 'Service temporarily unavailable. Please try again later.';\n            default:\n                return `Request failed with status ${error.status}`;\n        }\n    }\n    return 'An unexpected error occurred. Please try again.';\n}\n/**\n * Create and configure the query client\n */ function createQueryClient() {\n    return new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n        defaultOptions: defaultQueryOptions,\n        queryCache,\n        mutationCache\n    });\n}\n/**\n * Global query client instance\n */ const queryClient = createQueryClient();\n/**\n * Query client utilities for common operations\n */ const queryClientUtils = {\n    /**\n   * Invalidate queries by pattern\n   */ invalidateByPattern: async (pattern)=>{\n        await queryClient.invalidateQueries({\n            queryKey: pattern\n        });\n    },\n    /**\n   * Invalidate all queries for a specific entity\n   */ invalidateEntity: async (entity)=>{\n        await queryClient.invalidateQueries({\n            queryKey: [\n                entity\n            ]\n        });\n    },\n    /**\n   * Remove queries by pattern\n   */ removeByPattern: (pattern)=>{\n        queryClient.removeQueries({\n            queryKey: pattern\n        });\n    },\n    /**\n   * Set query data with optimistic updates\n   */ setOptimisticData: (queryKey, updater)=>{\n        queryClient.setQueryData(queryKey, updater);\n    },\n    /**\n   * Get cached query data\n   */ getCachedData: (queryKey)=>{\n        return queryClient.getQueryData(queryKey);\n    },\n    /**\n   * Prefetch query data\n   */ prefetch: async (queryKey, queryFn)=>{\n        await queryClient.prefetchQuery({\n            queryKey,\n            queryFn\n        });\n    },\n    /**\n   * Cancel outgoing queries\n   */ cancelQueries: async (queryKey)=>{\n        await queryClient.cancelQueries({\n            queryKey\n        });\n    },\n    /**\n   * Reset all queries and cache\n   */ resetAll: async ()=>{\n        await queryClient.resetQueries();\n    },\n    /**\n   * Clear all cache\n   */ clearCache: ()=>{\n        queryClient.clear();\n    },\n    /**\n   * Get query cache stats\n   */ getCacheStats: ()=>{\n        const cache = queryClient.getQueryCache();\n        const queries = cache.getAll();\n        return {\n            totalQueries: queries.length,\n            activeQueries: queries.filter((q)=>q.getObserversCount() > 0).length,\n            staleQueries: queries.filter((q)=>q.isStale()).length,\n            errorQueries: queries.filter((q)=>q.state.status === 'error').length,\n            loadingQueries: queries.filter((q)=>q.state.fetchStatus === 'fetching').length\n        };\n    }\n};\n/**\n * Development tools for debugging queries\n */ const queryDevTools = {\n    /**\n   * Log all queries and their states\n   */ logAllQueries: ()=>{\n        if (false) {}\n        const cache = queryClient.getQueryCache();\n        const queries = cache.getAll();\n        console.group('Query Cache State');\n        queries.forEach((query)=>{\n            console.log({\n                queryKey: query.queryKey,\n                status: query.state.status,\n                fetchStatus: query.state.fetchStatus,\n                dataUpdatedAt: new Date(query.state.dataUpdatedAt || 0),\n                error: query.state.error,\n                observers: query.getObserversCount()\n            });\n        });\n        console.groupEnd();\n    },\n    /**\n   * Monitor query performance\n   */ monitorPerformance: ()=>{\n        if (false) {}\n        const cache = queryClient.getQueryCache();\n        cache.subscribe((event)=>{\n            if (event.type === 'queryUpdated') {\n                const query = event.query;\n                const duration = Date.now() - query.state.fetchFailureReason?.timestamp || 0;\n                if (duration > 1000) {\n                    console.warn('Slow query detected:', {\n                        queryKey: query.queryKey,\n                        duration: `${duration}ms`\n                    });\n                }\n            }\n        });\n    },\n    /**\n   * Track cache hit/miss ratio\n   */ trackCacheHitRatio: ()=>{\n        if (false) {}\n        let hits = 0;\n        let misses = 0;\n        const cache = queryClient.getQueryCache();\n        cache.subscribe((event)=>{\n            if (event.type === 'queryUpdated') {\n                const query = event.query;\n                if (query.state.fetchStatus === 'fetching' && query.state.data !== undefined) {\n                    hits++;\n                } else if (query.state.fetchStatus === 'fetching') {\n                    misses++;\n                }\n                const total = hits + misses;\n                if (total > 0 && total % 10 === 0) {\n                    console.log(`Cache hit ratio: ${(hits / total * 100).toFixed(1)}% (${hits}/${total})`);\n                }\n            }\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/query-client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerClient: () => (/* binding */ createServerClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://placeholder.supabase.co\";\nconst supabaseAnonKey = \"placeholder-anon-key\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n// Server-side client for API routes\nconst createServerClient = ()=>{\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://placeholder.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/timezone.ts":
/*!*****************************!*\
  !*** ./src/lib/timezone.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TimezoneService: () => (/* binding */ TimezoneService),\n/* harmony export */   convertToAppTimezone: () => (/* binding */ convertToAppTimezone),\n/* harmony export */   formatInAppTimezone: () => (/* binding */ formatInAppTimezone),\n/* harmony export */   getCurrentTimezone: () => (/* binding */ getCurrentTimezone),\n/* harmony export */   timezoneService: () => (/* binding */ timezoneService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\nclass TimezoneService {\n    static getInstance() {\n        if (!TimezoneService.instance) {\n            TimezoneService.instance = new TimezoneService();\n        }\n        return TimezoneService.instance;\n    }\n    /**\n   * Get the current application timezone\n   */ async getCurrentTimezone() {\n        // Check cache first\n        if (this.cachedTimezone && Date.now() < this.cacheExpiry) {\n            return this.cachedTimezone;\n        }\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.createServerClient)();\n            const { data, error } = await supabase.from('timezone_config').select('timezone').order('updated_at', {\n                ascending: false\n            }).limit(1).single();\n            if (error || !data) {\n                console.warn('Failed to fetch timezone config, using default:', error);\n                return 'America/Chicago';\n            }\n            // Update cache\n            this.cachedTimezone = data.timezone;\n            this.cacheExpiry = Date.now() + this.CACHE_DURATION;\n            return data.timezone;\n        } catch (error) {\n            console.error('Error fetching timezone:', error);\n            return 'America/Chicago';\n        }\n    }\n    /**\n   * Get the current timezone configuration with full details\n   */ async getTimezoneConfig() {\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.createServerClient)();\n            const { data, error } = await supabase.from('timezone_config').select('*').order('updated_at', {\n                ascending: false\n            }).limit(1).single();\n            if (error || !data) {\n                console.warn('Failed to fetch timezone config:', error);\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error('Error fetching timezone config:', error);\n            return null;\n        }\n    }\n    /**\n   * Update the application timezone (admin only)\n   */ async updateTimezone(timezone, userId) {\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.createServerClient)();\n            // Validate timezone\n            const isValid = await this.validateTimezone(timezone);\n            if (!isValid) {\n                throw new Error('Invalid timezone provided');\n            }\n            const { error } = await supabase.from('timezone_config').update({\n                timezone,\n                updated_by: userId,\n                updated_at: new Date().toISOString()\n            }).eq('id', (await this.getTimezoneConfig())?.id);\n            if (error) {\n                console.error('Failed to update timezone:', error);\n                return false;\n            }\n            // Clear cache\n            this.cachedTimezone = null;\n            this.cacheExpiry = 0;\n            return true;\n        } catch (error) {\n            console.error('Error updating timezone:', error);\n            return false;\n        }\n    }\n    /**\n   * Get list of supported timezones\n   */ async getSupportedTimezones() {\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.createServerClient)();\n            const { data, error } = await supabase.from('supported_timezones').select('*').eq('is_active', true).order('region', {\n                ascending: true\n            }).order('display_name', {\n                ascending: true\n            });\n            if (error) {\n                console.error('Failed to fetch supported timezones:', error);\n                return [];\n            }\n            return data;\n        } catch (error) {\n            console.error('Error fetching supported timezones:', error);\n            return [];\n        }\n    }\n    /**\n   * Validate if a timezone is supported\n   */ async validateTimezone(timezone) {\n        try {\n            const supportedTimezones = await this.getSupportedTimezones();\n            return supportedTimezones.some((tz)=>tz.timezone === timezone);\n        } catch (error) {\n            console.error('Error validating timezone:', error);\n            return false;\n        }\n    }\n    /**\n   * Convert a UTC timestamp to the application timezone\n   */ async convertToAppTimezone(utcTimestamp) {\n        const timezone = await this.getCurrentTimezone();\n        const date = typeof utcTimestamp === 'string' ? new Date(utcTimestamp) : utcTimestamp;\n        try {\n            // Use Intl.DateTimeFormat for timezone conversion\n            const formatter = new Intl.DateTimeFormat('en-US', {\n                timeZone: timezone,\n                year: 'numeric',\n                month: '2-digit',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit',\n                second: '2-digit',\n                hour12: false\n            });\n            const parts = formatter.formatToParts(date);\n            const formattedDate = new Date(`${parts.find((p)=>p.type === 'year')?.value}-${parts.find((p)=>p.type === 'month')?.value}-${parts.find((p)=>p.type === 'day')?.value}T${parts.find((p)=>p.type === 'hour')?.value}:${parts.find((p)=>p.type === 'minute')?.value}:${parts.find((p)=>p.type === 'second')?.value}`);\n            return formattedDate;\n        } catch (error) {\n            console.error('Error converting timezone:', error);\n            return date;\n        }\n    }\n    /**\n   * Format a timestamp in the application timezone\n   */ async formatInAppTimezone(timestamp, options = {}) {\n        const timezone = await this.getCurrentTimezone();\n        const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;\n        const defaultOptions = {\n            timeZone: timezone,\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: true,\n            ...options\n        };\n        try {\n            return new Intl.DateTimeFormat('en-US', defaultOptions).format(date);\n        } catch (error) {\n            console.error('Error formatting timestamp:', error);\n            return date.toLocaleString();\n        }\n    }\n    /**\n   * Get timezone offset information\n   */ async getTimezoneOffset(timezone) {\n        const tz = timezone || await this.getCurrentTimezone();\n        const now = new Date();\n        try {\n            const formatter = new Intl.DateTimeFormat('en-US', {\n                timeZone: tz,\n                timeZoneName: 'short'\n            });\n            const parts = formatter.formatToParts(now);\n            const abbreviation = parts.find((part)=>part.type === 'timeZoneName')?.value || '';\n            // Calculate offset in minutes\n            const utcDate = new Date(now.toLocaleString('en-US', {\n                timeZone: 'UTC'\n            }));\n            const tzDate = new Date(now.toLocaleString('en-US', {\n                timeZone: tz\n            }));\n            const offset = (tzDate.getTime() - utcDate.getTime()) / (1000 * 60);\n            return {\n                offset,\n                abbreviation\n            };\n        } catch (error) {\n            console.error('Error getting timezone offset:', error);\n            return {\n                offset: 0,\n                abbreviation: 'UTC'\n            };\n        }\n    }\n    /**\n   * Clear timezone cache (useful when timezone is updated)\n   */ clearCache() {\n        this.cachedTimezone = null;\n        this.cacheExpiry = 0;\n    }\n    constructor(){\n        this.cachedTimezone = null;\n        this.cacheExpiry = 0;\n        this.CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n    }\n}\n// Export singleton instance\nconst timezoneService = TimezoneService.getInstance();\n// Utility functions for common operations\nconst getCurrentTimezone = ()=>timezoneService.getCurrentTimezone();\nconst formatInAppTimezone = (timestamp, options)=>timezoneService.formatInAppTimezone(timestamp, options);\nconst convertToAppTimezone = (timestamp)=>timezoneService.convertToAppTimezone(timestamp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/timezone.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiRDpcXEFJXFxjYW5kaWQtdGFzay1tZ210XFx0YXNrLW1hbmFnZW1lbnQtcHdhXFxzcmNcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCwgdHlwZSBDbGFzc1ZhbHVlIH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@tanstack","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/tailwind-merge","vendor-chunks/date-fns","vendor-chunks/whatwg-url","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/webidl-conversions","vendor-chunks/@swc","vendor-chunks/class-variance-authority","vendor-chunks/isows","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CAI%5Ccandid-task-mgmt%5Ctask-management-pwa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Ccandid-task-mgmt%5Ctask-management-pwa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();