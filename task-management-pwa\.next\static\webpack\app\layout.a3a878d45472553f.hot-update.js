"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ef8db40f728b\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcQUlcXGNhbmRpZC10YXNrLW1nbXRcXHRhc2stbWFuYWdlbWVudC1wd2FcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImVmOGRiNDBmNzI4YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/query-client.ts":
/*!*********************************!*\
  !*** ./src/lib/query-client.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQueryClient: () => (/* binding */ createQueryClient),\n/* harmony export */   queryClient: () => (/* binding */ queryClient),\n/* harmony export */   queryClientUtils: () => (/* binding */ queryClientUtils),\n/* harmony export */   queryDevTools: () => (/* binding */ queryDevTools)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n\n\n/**\n * Default query options for consistent behavior across the app\n */ const defaultQueryOptions = {\n    queries: {\n        // Cache data for 5 minutes by default\n        staleTime: 5 * 60 * 1000,\n        // Keep data in cache for 10 minutes after component unmount\n        gcTime: 10 * 60 * 1000,\n        // Retry failed requests 3 times with exponential backoff\n        retry: (failureCount, error)=>{\n            // Don't retry on 4xx errors (client errors)\n            if ((error === null || error === void 0 ? void 0 : error.status) >= 400 && (error === null || error === void 0 ? void 0 : error.status) < 500) {\n                return false;\n            }\n            // Retry up to 3 times for other errors\n            return failureCount < 3;\n        },\n        // Retry delay with exponential backoff\n        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),\n        // Refetch on window focus for critical data\n        refetchOnWindowFocus: (query)=>{\n            // Only refetch critical queries on window focus\n            const criticalQueries = [\n                'auth',\n                'timer',\n                'notifications'\n            ];\n            return criticalQueries.some((key)=>query.queryKey.some((k)=>typeof k === 'string' && k.includes(key)));\n        },\n        // Refetch on reconnect\n        refetchOnReconnect: true,\n        // Don't refetch on mount if data is fresh\n        refetchOnMount: (query)=>{\n            // Refetch if data is older than 1 minute\n            return Date.now() - (query.state.dataUpdatedAt || 0) > 60 * 1000;\n        }\n    },\n    mutations: {\n        // Retry mutations once\n        retry: 1,\n        // Retry delay for mutations\n        retryDelay: 1000\n    }\n};\n/**\n * Query cache configuration with global error handling\n */ const queryCache = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.QueryCache({\n    onError: (error, query)=>{\n        console.error('Query error:', error, 'Query:', query.queryKey);\n        // Don't show toast for background refetches\n        if (query.state.fetchStatus === 'fetching' && query.state.data !== undefined) {\n            return;\n        }\n        // Show user-friendly error messages\n        const errorMessage = getErrorMessage(error);\n        sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error('Error', {\n            description: errorMessage\n        });\n    },\n    onSuccess: (data, query)=>{\n        // Log successful queries in development\n        if (true) {\n            console.log('Query success:', query.queryKey, data);\n        }\n    }\n});\n/**\n * Mutation cache configuration with global error handling\n */ const mutationCache = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.MutationCache({\n    onError: (error, variables, context, mutation)=>{\n        console.error('Mutation error:', error, 'Variables:', variables);\n        // Show user-friendly error messages for mutations\n        const errorMessage = getErrorMessage(error);\n        sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error('Operation Failed', {\n            description: errorMessage\n        });\n    },\n    onSuccess: (data, variables, context, mutation)=>{\n        // Log successful mutations in development\n        if (true) {\n            console.log('Mutation success:', mutation.options.mutationKey, data);\n        }\n    }\n});\n/**\n * Extract user-friendly error messages from API responses\n */ function getErrorMessage(error) {\n    var _error_response_data_error, _error_response_data, _error_response;\n    // Handle network errors\n    if (!navigator.onLine) {\n        return 'You appear to be offline. Please check your connection.';\n    }\n    // Handle API errors\n    if (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : (_error_response_data_error = _error_response_data.error) === null || _error_response_data_error === void 0 ? void 0 : _error_response_data_error.message) {\n        return error.response.data.error.message;\n    }\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        return error.message;\n    }\n    // Handle HTTP status codes\n    if (error === null || error === void 0 ? void 0 : error.status) {\n        switch(error.status){\n            case 401:\n                return 'You are not authorized. Please log in again.';\n            case 403:\n                return 'You do not have permission to perform this action.';\n            case 404:\n                return 'The requested resource was not found.';\n            case 429:\n                return 'Too many requests. Please try again later.';\n            case 500:\n                return 'Server error. Please try again later.';\n            case 503:\n                return 'Service temporarily unavailable. Please try again later.';\n            default:\n                return \"Request failed with status \".concat(error.status);\n        }\n    }\n    return 'An unexpected error occurred. Please try again.';\n}\n/**\n * Create and configure the query client\n */ function createQueryClient() {\n    return new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n        defaultOptions: defaultQueryOptions,\n        queryCache,\n        mutationCache\n    });\n}\n/**\n * Global query client instance\n */ const queryClient = createQueryClient();\n/**\n * Query client utilities for common operations\n */ const queryClientUtils = {\n    /**\n   * Invalidate queries by pattern\n   */ invalidateByPattern: async (pattern)=>{\n        await queryClient.invalidateQueries({\n            queryKey: pattern\n        });\n    },\n    /**\n   * Invalidate all queries for a specific entity\n   */ invalidateEntity: async (entity)=>{\n        await queryClient.invalidateQueries({\n            queryKey: [\n                entity\n            ]\n        });\n    },\n    /**\n   * Remove queries by pattern\n   */ removeByPattern: (pattern)=>{\n        queryClient.removeQueries({\n            queryKey: pattern\n        });\n    },\n    /**\n   * Set query data with optimistic updates\n   */ setOptimisticData: (queryKey, updater)=>{\n        queryClient.setQueryData(queryKey, updater);\n    },\n    /**\n   * Get cached query data\n   */ getCachedData: (queryKey)=>{\n        return queryClient.getQueryData(queryKey);\n    },\n    /**\n   * Prefetch query data\n   */ prefetch: async (queryKey, queryFn)=>{\n        await queryClient.prefetchQuery({\n            queryKey,\n            queryFn\n        });\n    },\n    /**\n   * Cancel outgoing queries\n   */ cancelQueries: async (queryKey)=>{\n        await queryClient.cancelQueries({\n            queryKey\n        });\n    },\n    /**\n   * Reset all queries and cache\n   */ resetAll: async ()=>{\n        await queryClient.resetQueries();\n    },\n    /**\n   * Clear all cache\n   */ clearCache: ()=>{\n        queryClient.clear();\n    },\n    /**\n   * Get query cache stats\n   */ getCacheStats: ()=>{\n        const cache = queryClient.getQueryCache();\n        const queries = cache.getAll();\n        return {\n            totalQueries: queries.length,\n            activeQueries: queries.filter((q)=>q.getObserversCount() > 0).length,\n            staleQueries: queries.filter((q)=>q.isStale()).length,\n            errorQueries: queries.filter((q)=>q.state.status === 'error').length,\n            loadingQueries: queries.filter((q)=>q.state.fetchStatus === 'fetching').length\n        };\n    }\n};\n/**\n * Development tools for debugging queries\n */ const queryDevTools = {\n    /**\n   * Log all queries and their states\n   */ logAllQueries: ()=>{\n        if (false) {}\n        const cache = queryClient.getQueryCache();\n        const queries = cache.getAll();\n        console.group('Query Cache State');\n        queries.forEach((query)=>{\n            console.log({\n                queryKey: query.queryKey,\n                status: query.state.status,\n                fetchStatus: query.state.fetchStatus,\n                dataUpdatedAt: new Date(query.state.dataUpdatedAt || 0),\n                error: query.state.error,\n                observers: query.getObserversCount()\n            });\n        });\n        console.groupEnd();\n    },\n    /**\n   * Monitor query performance\n   */ monitorPerformance: ()=>{\n        if (false) {}\n        const cache = queryClient.getQueryCache();\n        cache.subscribe((event)=>{\n            if (event.type === 'queryUpdated') {\n                var _query_state_fetchFailureReason;\n                const query = event.query;\n                const duration = Date.now() - ((_query_state_fetchFailureReason = query.state.fetchFailureReason) === null || _query_state_fetchFailureReason === void 0 ? void 0 : _query_state_fetchFailureReason.timestamp) || 0;\n                if (duration > 1000) {\n                    console.warn('Slow query detected:', {\n                        queryKey: query.queryKey,\n                        duration: \"\".concat(duration, \"ms\")\n                    });\n                }\n            }\n        });\n    },\n    /**\n   * Track cache hit/miss ratio\n   */ trackCacheHitRatio: ()=>{\n        if (false) {}\n        let hits = 0;\n        let misses = 0;\n        const cache = queryClient.getQueryCache();\n        cache.subscribe((event)=>{\n            if (event.type === 'queryUpdated') {\n                const query = event.query;\n                if (query.state.fetchStatus === 'fetching' && query.state.data !== undefined) {\n                    hits++;\n                } else if (query.state.fetchStatus === 'fetching') {\n                    misses++;\n                }\n                const total = hits + misses;\n                if (total > 0 && total % 10 === 0) {\n                    console.log(\"Cache hit ratio: \".concat((hits / total * 100).toFixed(1), \"% (\").concat(hits, \"/\").concat(total, \")\"));\n                }\n            }\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/query-client.ts\n"));

/***/ })

});